const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

// 格式化金额，添加千分位分隔符
const formatMoney = (amount) => {
  if (amount === null || amount === undefined || amount === '') {
    return '0'
  }
  return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
}

// 导入环境配置
const { getConfig } = require('../config/env');

// API 基础配置 - 从环境配置中获取
const API_BASE_URL = getConfig().apiBaseUrl;

// 统一的API请求方法
const request = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    const { method = 'GET', data = {}, header = {} } = options

    // 获取用户token
    const token = wx.getStorageSync('token')
    if (token) {
      header['Authorization'] = `Bearer ${token}`
    }

    wx.request({
      url: `${API_BASE_URL}${url}`,
      method,
      data,
      header: {
        'Content-Type': 'application/json',
        ...header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`))
        }
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

// 用户登录相关API
const userAPI = {
  // 微信登录，获取openid和userid
  wxLogin: (code) => {
    return request('/user/wxLogin', {
      method: 'POST',
      data: { code }
    })
  },

  // 获取用户详细信息
  getUserInfo: (userId) => {
    return request(`/user/info/${userId}`)
  },

  // 更新用户信息
  updateUserInfo: (userId, userInfo) => {
    return request(`/user/info/${userId}`, {
      method: 'PUT',
      data: userInfo
    })
  },

  // 检查用户信息是否完整
  checkUserInfoComplete: (userId) => {
    return request(`/user/checkComplete`)
  }
}

// 会单相关API
const meetingAPI = {
  // 获取会单列表
  getMeetingList: (params) => {
    return request('/activity/list', {
      method: 'GET',
      data: params
    })
  },

  // 获取会单详情
  getMeetingDetail: (meetingId) => {
    return request(`/activity/info?id=${meetingId}`)
  },

  // 获取会单基本信息（用于邀请页面）
  getMeetingBasicInfo: (meetingId) => {
    return request(`/activity/basic?id=${meetingId}`)
  },

  bidMeeting: (meetingId, periodId, amount) => {
    return request(`/activity/bid`, {
      method: 'POST',
      data: {
        activityId: meetingId,
        periodId,
        amount
      }
    })
  },

  // 加入会单
  joinMeeting: (meetingId, userData = {}) => {
    return request(`/activity/join`, {
      method: 'POST',
      data: {
        meeting_id: meetingId,
        ...userData
      }
    })
  },

  // 创建会单
  createMeeting: (meetingData) => {
    return request('/activity/create', {
      method: 'POST',
      data: meetingData
    })
  },

  // 更新会期日期
  updatePeriodDate: (meetingId, periodChanges) => {
    return request(`/activity/updatePeriods`, {
      method: 'PUT',
      data: {
        meeting_id: meetingId,
        period_changes: periodChanges
      }
    })
  },

  // 移除会员
  removeMember: (meetingId, memberRemovals) => {
    return request(`/activity/removeMembers`, {
      method: 'PUT',
      data: {
        meeting_id: meetingId,
        member_removals: memberRemovals
      }
    })
  },

  // 批量更新会单（会期和会员）
  updateMeeting: (meetingId, updateData) => {
    return request(`/activity/update`, {
      method: 'PUT',
      data: {
        meeting_id: meetingId,
        ...updateData
      }
    })
  }
}

module.exports = {
  formatTime,
  formatMoney,
  request,
  userAPI,
  meetingAPI
}