// 用户认证和状态管理工具
const { userAPI } = require('./util.js')

class AuthManager {
  constructor() {
    this.loginPromise = null
    this.userInfo = null
    this.loginCallbacks = []
  }

  // 微信登录
  async wxLogin() {
    // 如果正在登录中，返回同一个Promise
    if (this.loginPromise) {
      return this.loginPromise
    }

    this.loginPromise = new Promise(async (resolve, reject) => {
      try {
        // 检查本地是否有有效的登录信息
        const localUserInfo = wx.getStorageSync('userInfo')
        const token = wx.getStorageSync('token')
        
        if (localUserInfo && token) {
          this.userInfo = localUserInfo
          console.log('使用本地登录信息:', localUserInfo)
          resolve(localUserInfo)
          this.notifyLoginCallbacks(localUserInfo)
          return
        }

        // 调用微信登录
        wx.login({
          success: async (loginRes) => {
            if (loginRes.code) {
              try {
                // 调用后端接口获取openid和userid
                const loginResult = await userAPI.wxLogin(loginRes.code)
                
                if (loginResult.success) {
                  const { openid, userId, token, userInfo } = loginResult.data
                  
                  // 保存登录信息到本地
                  wx.setStorageSync('openid', openid)
                  wx.setStorageSync('userId', userId)
                  wx.setStorageSync('token', token)
                  wx.setStorageSync('userInfo', userInfo)
                  
                  this.userInfo = userInfo
                  console.log('微信登录成功:', userInfo)
                  resolve(userInfo)
                  this.notifyLoginCallbacks(userInfo)
                } else {
                  throw new Error(loginResult.message || '登录失败')
                }
              } catch (apiError) {
                console.error('调用登录接口失败:', apiError)
                reject(apiError)
              }
            } else {
              reject(new Error('获取微信登录code失败'))
            }
          },
          fail: (error) => {
            console.error('微信登录失败:', error)
            reject(error)
          }
        })
      } catch (error) {
        console.error('登录过程出错:', error)
        reject(error)
      }
    })

    // 登录完成后清除Promise
    this.loginPromise.finally(() => {
      this.loginPromise = null
    })

    return this.loginPromise
  }

  // 获取用户信息
  async getUserInfo() {
    if (this.userInfo) {
      return this.userInfo
    }

    // 尝试从本地获取
    const localUserInfo = wx.getStorageSync('userInfo')
    if (localUserInfo) {
      this.userInfo = localUserInfo
      return localUserInfo
    }

    // 如果没有本地信息，触发登录
    return await this.wxLogin()
  }

  // 获取用户ID
  async getUserId() {
    const userInfo = await this.getUserInfo()
    return userInfo.userId || wx.getStorageSync('userId')
  }

  // 检查用户信息是否完整
  async checkUserInfoComplete() {
    try {
      const userId = await this.getUserId()
      if (!userId) {
        return false
      }

      const result = await userAPI.checkUserInfoComplete(userId)
      return result.data.isComplete
    } catch (error) {
      console.error('检查用户信息完整性失败:', error)
      return false
    }
  }

  // 更新用户信息
  async updateUserInfo(newUserInfo) {
    try {
      const userId = await this.getUserId()
      const result = await userAPI.updateUserInfo(userId, newUserInfo)
      
      if (result.success) {
        // 更新本地用户信息
        const updatedUserInfo = { ...this.userInfo, ...newUserInfo }
        this.userInfo = updatedUserInfo
        wx.setStorageSync('userInfo', updatedUserInfo)
        return updatedUserInfo
      } else {
        throw new Error(result.message || '更新用户信息失败')
      }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    }
  }

  // 检查是否需要完善用户信息
  async shouldCompleteUserInfo() {
    const isComplete = await this.checkUserInfoComplete()
    return !isComplete
  }

  // 注册登录完成回调
  onLoginComplete(callback) {
    this.loginCallbacks.push(callback)
  }

  // 移除登录完成回调
  offLoginComplete(callback) {
    const index = this.loginCallbacks.indexOf(callback)
    if (index > -1) {
      this.loginCallbacks.splice(index, 1)
    }
  }

  // 通知所有登录回调
  notifyLoginCallbacks(userInfo) {
    this.loginCallbacks.forEach(callback => {
      try {
        callback(userInfo)
      } catch (error) {
        console.error('登录回调执行失败:', error)
      }
    })
  }

  // 退出登录
  logout() {
    this.userInfo = null
    wx.clearStorageSync()
    console.log('用户已退出登录')
  }

  // 检查登录状态
  isLoggedIn() {
    const token = wx.getStorageSync('token')
    const userId = wx.getStorageSync('userId')
    return !!(token && userId)
  }
}

// 创建全局实例
const authManager = new AuthManager()

module.exports = authManager
