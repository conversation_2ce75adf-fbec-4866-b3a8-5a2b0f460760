// app.js
const authManager = require('./utils/auth.js')
const { getEnvInfo, getConfig } = require('./config/env')

App({
  onLaunch() {
    console.log('小程序启动')
    
    // 输出环境信息
    const envInfo = getEnvInfo();
    const config = getConfig();
    console.log('当前环境:', envInfo.env);
    console.log('API地址:', config.apiBaseUrl);
    
    if (config.debug) {
      console.log('环境详情:', {
        version: envInfo.version,
        appId: envInfo.appId,
        config: config
      });
    }

    console.log('小程序启动完成，等待页面主动调用登录')
  },

  // 提供给页面调用的登录方法
  async ensureLogin() {
    // 如果已经登录且有用户信息，直接返回
    if (this.globalData.isLoggedIn && this.globalData.userInfo) {
      console.log('已登录，直接返回用户信息')
      return this.globalData.userInfo
    }

    try {
      console.log('开始登录...')
      const userInfo = await authManager.wxLogin()
      this.globalData.userInfo = userInfo
      this.globalData.isLoggedIn = true
      console.log('登录成功:', userInfo)
      return userInfo
    } catch (error) {
      console.error('登录失败:', error)
      this.globalData.isLoggedIn = false
      throw error
    }
  },

  // 检查是否需要完善用户信息（需要先确保已登录）
  async checkAndCompleteUserInfo(scene = 'default') {
    try {
      // 先确保已登录
      await this.ensureLogin()

      const shouldComplete = await authManager.shouldCompleteUserInfo()

      if (shouldComplete) {
        console.log(`场景 ${scene} 需要完善用户信息`)
        wx.navigateTo({
          url: `/pages/complete-profile/index?scene=${scene}`
        })
        return false
      }

      return true
    } catch (error) {
      console.error('检查用户信息完整性失败:', error)
      return true // 出错时不阻止操作
    }
  },

  // 获取用户信息（先确保已登录）
  async getUserInfo() {
    if (this.globalData.userInfo) {
      return this.globalData.userInfo
    }

    // 如果没有用户信息，先登录
    return await this.ensureLogin()
  },

  // 获取用户ID（先确保已登录）
  async getUserId() {
    try {
      const userInfo = await this.getUserInfo()
      return userInfo.userId || await authManager.getUserId()
    } catch (error) {
      console.error('获取用户ID失败:', error)
      return null
    }
  },

  globalData: {
    userInfo: null,
    isLoggedIn: false
  }
})