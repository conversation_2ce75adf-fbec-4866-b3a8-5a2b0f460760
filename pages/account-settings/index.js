const app = getApp()

Page({
  data: {
    privacySettings: {
      profileVisible: true,
      phoneVisible: false,
      statsVisible: true
    },
    notificationSettings: {
      systemNotification: true,
      meetingReminder: true,
      paymentReminder: true
    },
    cacheSize: '12.5MB'
  },

  onLoad() {
    this.loadSettings()
  },

  // 加载设置数据
  async loadSettings() {
    try {
      // 这里可以从服务器或本地存储加载用户设置
      const settings = wx.getStorageSync('userSettings')
      if (settings) {
        this.setData({
          privacySettings: settings.privacy || this.data.privacySettings,
          notificationSettings: settings.notification || this.data.notificationSettings
        })
      }
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  },

  // 保存设置到本地存储
  saveSettings() {
    try {
      const settings = {
        privacy: this.data.privacySettings,
        notification: this.data.notificationSettings
      }
      wx.setStorageSync('userSettings', settings)
    } catch (error) {
      console.error('保存设置失败:', error)
    }
  },

  // 编辑个人资料
  editProfile() {
    wx.navigateTo({
      url: '/pages/complete-profile/index?scene=edit'
    })
  },

  // 修改密码
  changePassword() {
    wx.showModal({
      title: '修改密码',
      content: '此功能需要验证身份，是否继续？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  // 切换个人信息可见性
  toggleProfileVisible(e) {
    this.setData({
      'privacySettings.profileVisible': e.detail.value
    })
    this.saveSettings()
    
    wx.showToast({
      title: e.detail.value ? '已开启个人信息可见' : '已关闭个人信息可见',
      icon: 'none'
    })
  },

  // 切换手机号可见性
  togglePhoneVisible(e) {
    this.setData({
      'privacySettings.phoneVisible': e.detail.value
    })
    this.saveSettings()
    
    wx.showToast({
      title: e.detail.value ? '手机号已设为可见' : '手机号已设为不可见',
      icon: 'none'
    })
  },

  // 切换统计信息可见性
  toggleStatsVisible(e) {
    this.setData({
      'privacySettings.statsVisible': e.detail.value
    })
    this.saveSettings()
    
    wx.showToast({
      title: e.detail.value ? '统计信息已设为可见' : '统计信息已设为不可见',
      icon: 'none'
    })
  },

  // 切换系统通知
  toggleSystemNotification(e) {
    this.setData({
      'notificationSettings.systemNotification': e.detail.value
    })
    this.saveSettings()
    
    wx.showToast({
      title: e.detail.value ? '已开启系统通知' : '已关闭系统通知',
      icon: 'none'
    })
  },

  // 切换会单提醒
  toggleMeetingReminder(e) {
    this.setData({
      'notificationSettings.meetingReminder': e.detail.value
    })
    this.saveSettings()
    
    wx.showToast({
      title: e.detail.value ? '已开启会单提醒' : '已关闭会单提醒',
      icon: 'none'
    })
  },

  // 切换缴费提醒
  togglePaymentReminder(e) {
    this.setData({
      'notificationSettings.paymentReminder': e.detail.value
    })
    this.saveSettings()
    
    wx.showToast({
      title: e.detail.value ? '已开启缴费提醒' : '已关闭缴费提醒',
      icon: 'none'
    })
  },

  // 查看登录记录
  viewLoginHistory() {
    wx.showModal({
      title: '登录记录',
      content: '最近登录：\n2024-01-15 14:30 (当前设备)\n2024-01-14 09:15 (iPhone)\n2024-01-13 18:20 (Android)',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 清除缓存
  clearCache() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除应用缓存吗？这将删除临时文件和图片缓存。',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '清除中...'
          })
          
          // 模拟清除缓存
          setTimeout(() => {
            wx.hideLoading()
            this.setData({
              cacheSize: '0MB'
            })
            wx.showToast({
              title: '缓存已清除',
              icon: 'success'
            })
          }, 1500)
        }
      }
    })
  }
})
