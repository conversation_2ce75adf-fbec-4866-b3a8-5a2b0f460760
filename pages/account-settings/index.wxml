<view class="page">
  <!-- 个人信息区域 -->
  <view class="section">
    <view class="section-title">个人信息</view>
    <view class="setting-list">
      <view class="setting-item" bindtap="editProfile">
        <view class="setting-left">
          <view class="setting-icon">
            <text class="icon">👤</text>
          </view>
          <text class="setting-title">编辑个人资料</text>
        </view>
        <view class="setting-right">
          <text class="setting-value">姓名、头像、联系方式</text>
          <text class="arrow-icon">›</text>
        </view>
      </view>
      
      <view class="setting-item" bindtap="changePassword">
        <view class="setting-left">
          <view class="setting-icon">
            <text class="icon">🔐</text>
          </view>
          <text class="setting-title">修改密码</text>
        </view>
        <view class="setting-right">
          <text class="setting-value">定期更换密码保障安全</text>
          <text class="arrow-icon">›</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 隐私设置区域 -->
  <view class="section">
    <view class="section-title">隐私设置</view>
    <view class="setting-list">
      <view class="setting-item">
        <view class="setting-left">
          <view class="setting-icon">
            <text class="icon">👁️</text>
          </view>
          <text class="setting-title">个人信息可见性</text>
        </view>
        <view class="setting-right">
          <switch 
            checked="{{privacySettings.profileVisible}}"
            bindchange="toggleProfileVisible"
            color="#3b7cf7"
          />
        </view>
      </view>
      
      <view class="setting-item">
        <view class="setting-left">
          <view class="setting-icon">
            <text class="icon">📱</text>
          </view>
          <text class="setting-title">手机号可见</text>
        </view>
        <view class="setting-right">
          <switch 
            checked="{{privacySettings.phoneVisible}}"
            bindchange="togglePhoneVisible"
            color="#3b7cf7"
          />
        </view>
      </view>
      
      <view class="setting-item">
        <view class="setting-left">
          <view class="setting-icon">
            <text class="icon">📊</text>
          </view>
          <text class="setting-title">会单统计可见</text>
        </view>
        <view class="setting-right">
          <switch 
            checked="{{privacySettings.statsVisible}}"
            bindchange="toggleStatsVisible"
            color="#3b7cf7"
          />
        </view>
      </view>
    </view>
  </view>

  <!-- 通知设置区域 -->
  <view class="section">
    <view class="section-title">通知设置</view>
    <view class="setting-list">
      <view class="setting-item">
        <view class="setting-left">
          <view class="setting-icon">
            <text class="icon">🔔</text>
          </view>
          <text class="setting-title">系统通知</text>
        </view>
        <view class="setting-right">
          <switch 
            checked="{{notificationSettings.systemNotification}}"
            bindchange="toggleSystemNotification"
            color="#3b7cf7"
          />
        </view>
      </view>
      
      <view class="setting-item">
        <view class="setting-left">
          <view class="setting-icon">
            <text class="icon">💰</text>
          </view>
          <text class="setting-title">会单提醒</text>
        </view>
        <view class="setting-right">
          <switch 
            checked="{{notificationSettings.meetingReminder}}"
            bindchange="toggleMeetingReminder"
            color="#3b7cf7"
          />
        </view>
      </view>
      
      <view class="setting-item">
        <view class="setting-left">
          <view class="setting-icon">
            <text class="icon">⏰</text>
          </view>
          <text class="setting-title">缴费提醒</text>
        </view>
        <view class="setting-right">
          <switch 
            checked="{{notificationSettings.paymentReminder}}"
            bindchange="togglePaymentReminder"
            color="#3b7cf7"
          />
        </view>
      </view>
    </view>
  </view>

  <!-- 安全设置区域 -->
  <view class="section">
    <view class="section-title">安全设置</view>
    <view class="setting-list">
      <view class="setting-item" bindtap="viewLoginHistory">
        <view class="setting-left">
          <view class="setting-icon">
            <text class="icon">📋</text>
          </view>
          <text class="setting-title">登录记录</text>
        </view>
        <view class="setting-right">
          <text class="setting-value">查看最近登录记录</text>
          <text class="arrow-icon">›</text>
        </view>
      </view>
      
      <view class="setting-item" bindtap="clearCache">
        <view class="setting-left">
          <view class="setting-icon">
            <text class="icon">🗑️</text>
          </view>
          <text class="setting-title">清除缓存</text>
        </view>
        <view class="setting-right">
          <text class="setting-value">{{cacheSize}}</text>
          <text class="arrow-icon">›</text>
        </view>
      </view>
    </view>
  </view>
</view>
