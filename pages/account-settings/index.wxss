.page {
  min-height: 100vh;
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  padding-bottom: 40rpx;
}

/* 区域标题 */
.section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  padding: 32rpx 32rpx 16rpx;
}

/* 设置列表 */
.setting-list {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 0 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #f3f4f6;
  transition: background-color 0.2s ease;
  min-height: 120rpx;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: #f8f9fa;
}

/* 设置项左侧 */
.setting-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f4ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.setting-icon .icon {
  font-size: 40rpx;
  color: #3b7cf7;
}

.setting-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #1f2937;
}

/* 设置项右侧 */
.setting-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.setting-value {
  font-size: 28rpx;
  color: #6b7280;
  max-width: 300rpx;
  text-align: right;
  line-height: 1.4;
}

.arrow-icon {
  font-size: 40rpx;
  color: #d1d5db;
  margin-left: 8rpx;
}

/* 开关样式调整 */
switch {
  transform: scale(0.8);
}

/* 响应式调整 */
@media (max-width: 375px) {
  .setting-item {
    padding: 24rpx 32rpx;
    min-height: 100rpx;
  }
  
  .setting-icon {
    width: 64rpx;
    height: 64rpx;
    margin-right: 20rpx;
  }
  
  .setting-icon .icon {
    font-size: 32rpx;
  }
  
  .setting-title {
    font-size: 28rpx;
  }
  
  .setting-value {
    font-size: 24rpx;
    max-width: 200rpx;
  }
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* 成功状态动画 */
.success-animation {
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(1);
    background-color: #ffffff;
  }
  50% {
    transform: scale(1.02);
    background-color: #f0f9ff;
  }
  100% {
    transform: scale(1);
    background-color: #ffffff;
  }
}
