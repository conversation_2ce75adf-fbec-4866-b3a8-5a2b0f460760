.page {
  min-height: 100vh;
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  padding-bottom: 40rpx;
}

/* 应用头部 */
.app-header {
  background: linear-gradient(135deg, #3b7cf7 0%, #5a92f5 100%);
  padding: 60rpx 32rpx 40rpx;
  text-align: center;
  color: white;
  margin-bottom: 32rpx;
}

.app-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
  backdrop-filter: blur(10rpx);
}

.logo-icon {
  font-size: 64rpx;
}

.app-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.app-name {
  font-size: 40rpx;
  font-weight: 600;
}

.app-version {
  font-size: 24rpx;
  opacity: 0.8;
}

.app-desc {
  font-size: 28rpx;
  opacity: 0.9;
  margin-top: 8rpx;
}

/* 区域标题 */
.section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #374151;
  padding: 0 32rpx 16rpx;
}

/* 介绍卡片 */
.intro-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 0 32rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.intro-text {
  display: block;
  font-size: 28rpx;
  color: #374151;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

.intro-text:last-child {
  margin-bottom: 0;
}

/* 功能列表 */
.feature-list {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 0 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #f3f4f6;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f4ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.feature-icon .icon {
  font-size: 40rpx;
  color: #3b7cf7;
}

.feature-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.feature-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
}

.feature-desc {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

/* 版本信息列表 */
.version-list {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 0 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.version-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #f3f4f6;
}

.version-item:last-child {
  border-bottom: none;
}

.version-left {
  display: flex;
  align-items: center;
}

.version-title {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}

.version-value {
  font-size: 28rpx;
  color: #6b7280;
  text-align: right;
  max-width: 400rpx;
  line-height: 1.4;
}

/* 法律条款列表 */
.legal-list {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 0 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.legal-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.legal-item:last-child {
  border-bottom: none;
}

.legal-item:active {
  background-color: #f8f9fa;
}

.legal-left {
  display: flex;
  align-items: center;
}

.legal-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: #f0f4ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.legal-icon .icon {
  font-size: 32rpx;
  color: #3b7cf7;
}

.legal-title {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}

.arrow-icon {
  font-size: 40rpx;
  color: #d1d5db;
}

/* 联系我们卡片 */
.contact-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 0 32rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-label {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
  min-width: 160rpx;
}

.contact-value {
  font-size: 28rpx;
  color: #3b7cf7;
  flex: 1;
}

/* 版权信息 */
.copyright {
  text-align: center;
  padding: 40rpx 32rpx;
  margin-top: 40rpx;
}

.copyright-text {
  display: block;
  font-size: 24rpx;
  color: #9ca3af;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.copyright-text:last-child {
  margin-bottom: 0;
}
