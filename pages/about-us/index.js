const app = getApp()

Page({
  data: {
    appVersion: '1.0.0',
    releaseDate: '2024-01-15'
  },

  onLoad() {
    this.loadAppInfo()
  },

  // 加载应用信息
  loadAppInfo() {
    // 这里可以从服务器获取最新的版本信息
    const appInfo = wx.getAccountInfoSync()
    if (appInfo && appInfo.miniProgram) {
      this.setData({
        appVersion: appInfo.miniProgram.version || '1.0.0'
      })
    }
  },

  // 查看用户协议
  viewUserAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '用户协议详细内容正在完善中，您可以通过客服热线了解详情。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 查看隐私政策
  viewPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '我们严格保护用户隐私，详细的隐私政策内容正在完善中。如有疑问，请联系客服。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 查看服务条款
  viewServiceTerms() {
    wx.showModal({
      title: '服务条款',
      content: '服务条款详细内容正在完善中，您可以通过客服热线了解详情。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 复制邮箱
  copyEmail() {
    wx.setClipboardData({
      data: '<EMAIL>',
      success: () => {
        wx.showToast({
          title: '邮箱已复制',
          icon: 'success'
        })
      }
    })
  },

  // 拨打电话
  callPhone() {
    wx.makePhoneCall({
      phoneNumber: '************',
      fail: () => {
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        })
      }
    })
  }
})
