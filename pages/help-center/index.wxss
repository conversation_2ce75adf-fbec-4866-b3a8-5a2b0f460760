.page {
  min-height: 100vh;
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  padding-bottom: 40rpx;
}

/* 搜索区域 */
.search-section {
  padding: 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e7eb;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f3f4f6;
  border-radius: 24rpx;
  padding: 0 32rpx;
  height: 80rpx;
}

.search-icon {
  font-size: 32rpx;
  color: #9ca3af;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #374151;
}

.search-input::placeholder {
  color: #9ca3af;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  background-color: #ffffff;
  padding: 40rpx 32rpx;
  margin-bottom: 32rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.action-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b7cf7 0%, #5a92f5 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(59, 124, 247, 0.3);
}

.action-icon .icon {
  font-size: 48rpx;
  color: #ffffff;
}

.action-title {
  font-size: 24rpx;
  color: #374151;
  font-weight: 500;
}

/* 区域标题 */
.section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #374151;
  padding: 0 32rpx 16rpx;
}

/* FAQ列表 */
.faq-list {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 0 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.faq-item {
  border-bottom: 1rpx solid #f3f4f6;
  transition: all 0.3s ease;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-item.expanded {
  background-color: #f8f9fa;
}

.faq-question {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  cursor: pointer;
}

.question-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  flex: 1;
  line-height: 1.5;
}

.expand-icon {
  font-size: 40rpx;
  color: #3b7cf7;
  font-weight: 300;
  margin-left: 16rpx;
}

.faq-answer {
  padding: 0 40rpx 32rpx;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 使用指南列表 */
.guide-list {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 0 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.guide-item {
  display: flex;
  align-items: center;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.guide-item:last-child {
  border-bottom: none;
}

.guide-item:active {
  background-color: #f8f9fa;
}

.guide-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f4ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.guide-icon .icon {
  font-size: 40rpx;
  color: #3b7cf7;
}

.guide-content {
  flex: 1;
}

.guide-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8rpx;
}

.guide-desc {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

.arrow-icon {
  font-size: 40rpx;
  color: #d1d5db;
  margin-left: 16rpx;
}

/* 联系我们列表 */
.contact-list {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 0 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.contact-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #f3f4f6;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.contact-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f4ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.contact-icon .icon {
  font-size: 40rpx;
  color: #3b7cf7;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.contact-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
}

.contact-value {
  font-size: 24rpx;
  color: #6b7280;
}

.contact-btn {
  background-color: #3b7cf7;
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.contact-btn:active {
  background-color: #2563eb;
}

.contact-btn::after {
  border: none;
}
