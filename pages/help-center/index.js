const app = getApp()

Page({
  data: {
    searchKeyword: '',
    faqList: [
      {
        id: 1,
        question: '什么是互助会单？',
        answer: '互助会单是一种传统的民间互助金融形式，通过成员间的相互帮助，实现资金的合理配置和使用。每期由一名成员获得资金使用权，其他成员按约定缴费。',
        expanded: false
      },
      {
        id: 2,
        question: '如何创建一个会单？',
        answer: '在首页点击"创建会单"按钮，填写会单基本信息（名称、金额、期数、缴费周期等），设置会单规则，邀请成员加入即可。创建成功后您将成为会东。',
        expanded: false
      },
      {
        id: 3,
        question: '如何加入别人的会单？',
        answer: '您可以通过会东分享的邀请链接加入会单，或在首页搜索会单名称。加入前请仔细阅读会单规则，确认无误后申请加入，等待会东审核通过。',
        expanded: false
      },
      {
        id: 4,
        question: '投标是什么意思？',
        answer: '投标是决定当期资金使用者的方式。成员可以出价竞争当期资金使用权，出价最高者获得资金，同时需要承担相应的利息成本。',
        expanded: false
      },
      {
        id: 5,
        question: '如果有成员不按时缴费怎么办？',
        answer: '系统会自动发送缴费提醒。如成员逾期未缴费，会东可以联系该成员协商解决。严重违约的成员可能被移出会单，并承担相应的违约责任。',
        expanded: false
      }
    ],
    guideList: [
      {
        id: 1,
        icon: '🚀',
        title: '新手入门指南',
        description: '了解互助会单的基本概念和操作流程'
      },
      {
        id: 2,
        icon: '💰',
        title: '会单创建教程',
        description: '详细介绍如何创建和管理会单'
      },
      {
        id: 3,
        icon: '👥',
        title: '成员管理指南',
        description: '学习如何邀请和管理会单成员'
      },
      {
        id: 4,
        icon: '📊',
        title: '投标规则说明',
        description: '掌握投标的规则和技巧'
      },
      {
        id: 5,
        icon: '🔒',
        title: '安全使用须知',
        description: '了解平台安全机制和注意事项'
      }
    ]
  },

  onLoad() {
    // 页面加载时的初始化
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  // 执行搜索
  onSearch() {
    const keyword = this.data.searchKeyword.trim()
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      })
      return
    }
    
    // 这里可以实现搜索逻辑
    wx.showToast({
      title: `搜索"${keyword}"`,
      icon: 'none'
    })
  },

  // 联系在线客服
  contactService() {
    wx.showModal({
      title: '在线客服',
      content: '即将为您转接在线客服，请稍候...',
      showCancel: false,
      success: () => {
        wx.showToast({
          title: '客服功能开发中',
          icon: 'none'
        })
      }
    })
  },

  // 拨打客服电话
  callService() {
    wx.makePhoneCall({
      phoneNumber: '************',
      fail: () => {
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        })
      }
    })
  },

  // 意见反馈
  feedbackSuggestion() {
    wx.showModal({
      title: '意见反馈',
      content: '感谢您的宝贵意见！请通过邮箱或客服热线联系我们，我们会认真对待每一条反馈。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 切换FAQ展开状态
  toggleFaq(e) {
    const id = e.currentTarget.dataset.id
    const faqList = this.data.faqList.map(item => {
      if (item.id === id) {
        return { ...item, expanded: !item.expanded }
      }
      return item
    })
    
    this.setData({
      faqList
    })
  },

  // 查看使用指南
  viewGuide(e) {
    const id = e.currentTarget.dataset.id
    const guide = this.data.guideList.find(item => item.id === id)
    
    wx.showModal({
      title: guide.title,
      content: '详细的使用指南内容正在完善中，敬请期待！',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 复制邮箱
  copyEmail() {
    wx.setClipboardData({
      data: '<EMAIL>',
      success: () => {
        wx.showToast({
          title: '邮箱已复制',
          icon: 'success'
        })
      }
    })
  },

  // 拨打电话
  callPhone() {
    wx.makePhoneCall({
      phoneNumber: '************',
      fail: () => {
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        })
      }
    })
  }
})
