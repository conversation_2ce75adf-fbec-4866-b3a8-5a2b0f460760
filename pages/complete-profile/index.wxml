<view class="page">
  <!-- 顶部说明 -->
  <view class="header">
    <view class="icon">👤</view>
    <view class="title">完善个人信息</view>
    <view class="description">{{getSceneDescription()}}</view>
  </view>

  <!-- 表单区域 -->
  <view class="form-container">
    <!-- 姓名输入 -->
    <view class="form-item">
      <view class="label">
        <text class="required">*</text>
        <text>真实姓名</text>
      </view>
      <input 
        class="input {{nameError ? 'error' : ''}}"
        type="text"
        placeholder="请输入真实姓名"
        value="{{userInfo.name}}"
        bindinput="onNameInput"
        maxlength="20"
      />
      <view class="error-text" wx:if="{{nameError}}">{{nameError}}</view>
    </view>

    <!-- 手机号输入 -->
    <view class="form-item">
      <view class="label">
        <text class="required">*</text>
        <text>手机号码</text>
      </view>
      <input 
        class="input {{phoneError ? 'error' : ''}}"
        type="number"
        placeholder="请输入手机号码"
        value="{{userInfo.phone}}"
        bindinput="onPhoneInput"
        maxlength="11"
      />
      <view class="error-text" wx:if="{{phoneError}}">{{phoneError}}</view>
    </view>

    <!-- 提示信息 -->
    <view class="tips">
      <view class="tip-item">
        <text class="tip-icon">🔒</text>
        <text class="tip-text">您的个人信息将被严格保护</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">✅</text>
        <text class="tip-text">完善信息后可正常使用所有功能</text>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="footer">
    <button 
      class="submit-btn {{isSubmitting ? 'loading' : ''}}"
      bindtap="onSubmit"
      disabled="{{isSubmitting}}"
    >
      <text wx:if="{{!isSubmitting}}">保存信息</text>
      <text wx:else>保存中...</text>
    </button>
    
    <button class="skip-btn" bindtap="onSkip">暂时跳过</button>
  </view>
</view>
