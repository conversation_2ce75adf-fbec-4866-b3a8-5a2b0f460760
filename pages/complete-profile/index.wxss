.page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
}

/* 顶部说明区域 */
.header {
  background: linear-gradient(180deg, #5a92f5 0%, #3b7cf7 100%);
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  color: white;
}

.icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.description {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.4;
}

/* 表单容器 */
.form-container {
  padding: 40rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 40rpx;
}

.label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.required {
  color: #ff4757;
  margin-right: 8rpx;
  font-size: 24rpx;
}

.input {
  width: 100%;
  height: 88rpx;
  background-color: #ffffff;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.input:focus {
  border-color: #3b7cf7;
}

.input.error {
  border-color: #ff4757;
}

.input::placeholder {
  color: #9ca3af;
}

.error-text {
  color: #ff4757;
  font-size: 24rpx;
  margin-top: 8rpx;
  margin-left: 8rpx;
}

/* 提示信息 */
.tips {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-top: 40rpx;
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.4;
}

/* 底部按钮区域 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 32rpx 40rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #e5e7eb;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(90deg, #5a92f5 0%, #3b7cf7 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
}

.submit-btn.loading {
  opacity: 0.7;
}

.submit-btn:disabled {
  opacity: 0.7;
}

.skip-btn {
  width: 100%;
  height: 88rpx;
  background-color: transparent;
  color: #6b7280;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.skip-btn:active {
  background-color: #f9fafb;
}
