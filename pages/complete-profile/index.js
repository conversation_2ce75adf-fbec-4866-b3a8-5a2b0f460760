// pages/complete-profile/index.js
const authManager = require('../../utils/auth.js')

Page({
  data: {
    scene: 'default', // 触发场景
    meetingId: '', // 会单ID（从加入会单页面跳转时传递）
    userInfo: {
      name: '',
      phone: ''
    },
    isSubmitting: false,
    nameError: '',
    phoneError: ''
  },

  onLoad(options) {
    const { scene = 'default', meetingId } = options
    this.setData({
      scene,
      meetingId: meetingId || ''
    })

    console.log('完善用户信息页面加载，场景:', scene, '会单ID:', meetingId)
    this.loadExistingUserInfo()
  },

  // 加载已有的用户信息
  async loadExistingUserInfo() {
    try {
      console.log('完善信息页面开始加载用户信息');
      const app = getApp();
      const userInfo = await app.ensureLogin();
      console.log('登录成功，用户信息:', userInfo);

      if (userInfo) {
        this.setData({
          'userInfo.name': userInfo.name || '',
          'userInfo.phone': userInfo.phone || ''
        })
      }
    } catch (error) {
      console.error('加载用户信息失败:', error)
    }
  },

  // 输入姓名
  onNameInput(e) {
    const name = e.detail.value.trim()
    this.setData({
      'userInfo.name': name,
      nameError: ''
    })
  },

  // 输入手机号
  onPhoneInput(e) {
    const phone = e.detail.value.trim()
    this.setData({
      'userInfo.phone': phone,
      phoneError: ''
    })
  },

  // 验证表单
  validateForm() {
    const { userInfo } = this.data
    let isValid = true

    // 验证姓名
    if (!userInfo.name) {
      this.setData({ nameError: '请输入真实姓名' })
      isValid = false
    } else if (userInfo.name.length < 2) {
      this.setData({ nameError: '姓名至少2个字符' })
      isValid = false
    }

    // 验证手机号
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!userInfo.phone) {
      this.setData({ phoneError: '请输入手机号' })
      isValid = false
    } else if (!phoneRegex.test(userInfo.phone)) {
      this.setData({ phoneError: '请输入正确的手机号' })
      isValid = false
    }

    return isValid
  },

  // 提交用户信息
  async onSubmit() {
    if (this.data.isSubmitting) {
      return
    }

    if (!this.validateForm()) {
      return
    }

    this.setData({ isSubmitting: true })

    try {
      const { userInfo } = this.data

      // 更新用户信息
      await authManager.updateUserInfo({
        name: userInfo.name,
        phone: userInfo.phone
      })

      wx.showToast({
        title: '信息保存成功',
        icon: 'success',
        duration: 1500
      })

      // 延迟返回上一页或跳转到加入会单页面
      setTimeout(() => {
        if (this.data.scene === 'join-meeting' && this.data.meetingId) {
          // 如果是从加入会单页面跳转过来的，返回加入会单页面
          wx.navigateTo({
            url: `/pages/join-meeting/index?meetingId=${this.data.meetingId}`
          })
        } else {
          wx.navigateBack()
        }
      }, 1500)

    } catch (error) {
      console.error('保存用户信息失败:', error)
      wx.showToast({
        title: error.message || '保存失败，请重试',
        icon: 'none',
        duration: 2000
      })
    } finally {
      this.setData({ isSubmitting: false })
    }
  },

  // 跳过完善信息
  onSkip() {
    wx.showModal({
      title: '提示',
      content: '跳过完善信息可能会影响部分功能的使用，确定要跳过吗？',
      confirmText: '确定跳过',
      cancelText: '继续完善',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack()
        }
      }
    })
  },

  // 获取场景描述
  getSceneDescription() {
    const { scene } = this.data
    const sceneMap = {
      'create-meeting': '创建会单需要完善个人信息',
      'join-meeting': '加入会单需要完善个人信息',
      'bid': '投标需要完善个人信息',
      'edit': '编辑个人信息',
      'default': '为了更好地使用服务，请完善个人信息'
    }
    return sceneMap[scene] || sceneMap['default']
  }
})
