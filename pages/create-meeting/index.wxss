.page {
  height: 100vh;
  background-color: #F5F7FA;
  display: flex;
  flex-direction: column;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3B7CF7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 页面包装器 */
.page-wrapper {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 主体内容区域 */
.content-container {
  flex: 1;
  padding: 30rpx;
}

/* 表单区域 */
.form-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 表单项 */
.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.label .required {
  margin-right: 8rpx;
  color: #FF4757;
  font-size: 24rpx;
}

.input, .picker {
  width: 100%;
  height: 88rpx;
  background-color: #F8F9FA;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
  line-height: 40rpx;
}

/* 单独处理input元素的垂直居中 */
.input {
  padding-top: 24rpx;
  padding-bottom: 24rpx;
  padding-left: 24rpx;
  padding-right: 24rpx;
}

/* input placeholder样式 */
.input-placeholder {
  color: #9CA3AF !important;
  font-size: 28rpx !important;
  line-height: 40rpx !important;
  text-align: left !important;
}

/* 单独处理picker元素的垂直居中 */
.picker {
  display: flex;
  align-items: center;
  padding: 24rpx;
}

.input:focus {
  border-color: #3B7CF7;
  background-color: #ffffff;
}

.input.error, .picker.error {
  border-color: #FF4757;
}

.picker-text {
  color: #333;
  line-height: 40rpx;
  display: flex;
  align-items: center;
  height: 100%;
}

.picker-text:empty::before {
  content: attr(placeholder);
  color: #9CA3AF;
}

.error-text {
  color: #FF4757;
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 总金额展示区域 */
.total-amount-display {
  background: linear-gradient(135deg, #3B7CF7 0%, #5A92F5 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx 0 40rpx 0;
  text-align: center;
  color: white;
}

.total-label {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 8rpx;
}

.total-value {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
}

.total-formula {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 期数预览按钮 */
.period-preview-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #F8F9FF;
  border: 2rpx solid #3B7CF7;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-top: 40rpx;
}

.preview-text {
  color: #3B7CF7;
  font-size: 28rpx;
  font-weight: 500;
}

.preview-arrow {
  color: #3B7CF7;
  font-size: 32rpx;
  font-weight: bold;
}

/* 底部按钮区 */
.footer-buttons {
  display: flex;
  gap: 20rpx;
  padding: 32rpx 30rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  background-color: white;
  border-top: 1rpx solid #E5E7EB;
}

.reset-btn {
  flex: 1;
  height: 88rpx;
  background-color: transparent;
  color: #666;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.create-btn {
  flex: 2;
  height: 88rpx;
  background: linear-gradient(90deg, #5A92F5 0%, #3B7CF7 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
}

.create-btn.loading {
  opacity: 0.7;
}

.create-btn:disabled {
  opacity: 0.7;
}

/* 期数预览弹窗 */
.period-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.period-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  max-height: 80vh;
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  display: flex;
  flex-direction: column;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.period-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #E5E7EB;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #666;
}

.modal-body {
  flex: 1;
  padding: 20rpx 30rpx;
  max-height: 60vh;
}

.period-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.period-item:last-child {
  border-bottom: none;
}

.period-info {
  flex: 1;
}

.period-number {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.period-datetime {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.period-date {
  font-size: 26rpx;
  color: #666;
}

.period-time {
  font-size: 26rpx;
  color: #3B7CF7;
}

.period-edit {
  padding: 12rpx 24rpx;
  background-color: #F8F9FF;
  border: 1rpx solid #3B7CF7;
  border-radius: 8rpx;
}

.edit-text {
  font-size: 24rpx;
  color: #3B7CF7;
}

.modal-footer {
  padding: 30rpx;
  border-top: 1rpx solid #E5E7EB;
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(90deg, #5A92F5 0%, #3B7CF7 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
}
