// pages/create-meeting/index.js
const { formatMoney } = require('../../utils/util.js');

Page({
  data: {
    // 基本信息
    meetingInfo: {
      title: '',
      totalPeriods: '',
      baseAmount: '', // 底标金额
      startDate: '',
      intervalMonths: 3, // 间隔月份
      bidTime: '13:00' // 投标时间
    },

    // 计算属性
    totalAmount: '', // 总金额（自动计算）

    // 期数详情
    periodDetails: [], // 期数详情列表
    showPeriodModal: false, // 是否显示期数预览弹窗

    // 表单验证错误
    errors: {},

    // 页面状态
    isLoading: true,
    isLoggedIn: false,
    isSubmitting: false,

    // 选择器数据
    periodOptions: Array.from({ length: 24 }, (_, i) => ({ value: i + 1, label: `${i + 1}期` })),
    intervalOptions: Array.from({ length: 12 }, (_, i) => ({ value: i + 1, label: `${i + 1}个月` })),

    // 当前选择器显示值
    currentPeriodLabel: '请选择总期数',
    currentIntervalLabel: '1个月'
  },

  onLoad() {
    console.log('创建会单页面 onLoad 开始');
    this.loginAndLoadData();
    console.log('创建会单页面 onLoad 完成');
  },



  // 登录并加载数据
  async loginAndLoadData() {
    console.log('创建会单页面开始登录');
    this.setData({ isLoading: true, isLoggedIn: false });

    try {
      const app = getApp();
      console.log('调用 app.ensureLogin()');
      const userInfo = await app.ensureLogin();
      console.log('登录成功，用户信息:', userInfo);

      this.setData({ isLoggedIn: true });
      this.initializeForm();
      this.setData({ isLoading: false });
    } catch (error) {
      console.error('登录失败:', error);
      this.setData({ isLoading: false });
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    }
  },

  // 初始化表单
  initializeForm() {
    const today = new Date();
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
    const startDate = nextMonth.toISOString().split('T')[0];

    this.setData({
      'meetingInfo.startDate': startDate
    });
  },

  // 输入处理
  onTitleInput(e) {
    this.setData({
      'meetingInfo.title': e.detail.value,
      'errors.title': ''
    });
  },

  onBaseAmountInput(e) {
    const value = e.detail.value;
    this.setData({
      'meetingInfo.baseAmount': value,
      'errors.baseAmount': ''
    });
    this.calculateTotalAmount();
  },

  onTotalPeriodsChange(e) {
    const index = e.detail.value;
    const periods = this.data.periodOptions[index].value;
    const label = this.data.periodOptions[index].label;
    this.setData({
      'meetingInfo.totalPeriods': periods,
      'errors.totalPeriods': '',
      currentPeriodLabel: label
    });
    this.calculateTotalAmount();
    this.generatePeriodDetails();
  },

  // 计算总金额
  calculateTotalAmount() {
    const { baseAmount, totalPeriods } = this.data.meetingInfo;
    if (baseAmount && totalPeriods) {
      const total = parseFloat(baseAmount) * parseInt(totalPeriods);
      this.setData({
        totalAmount: formatMoney(total)
      });
    } else {
      this.setData({
        totalAmount: ''
      });
    }
  },

  onStartDateChange(e) {
    this.setData({
      'meetingInfo.startDate': e.detail.value,
      'errors.startDate': ''
    });
    this.generatePeriodDetails();
  },

  onIntervalMonthsChange(e) {
    const index = e.detail.value;
    const months = this.data.intervalOptions[index].value;
    const label = this.data.intervalOptions[index].label;
    this.setData({
      'meetingInfo.intervalMonths': months,
      currentIntervalLabel: label
    });
    this.generatePeriodDetails();
  },

  onBidTimeChange(e) {
    this.setData({
      'meetingInfo.bidTime': e.detail.value,
      'errors.bidTime': ''
    });
  },

  // 生成期数详情
  generatePeriodDetails() {
    const { totalPeriods, startDate, intervalMonths, bidTime } = this.data.meetingInfo;

    if (!totalPeriods || !startDate || !intervalMonths) {
      this.setData({ periodDetails: [] });
      return;
    }

    const details = [];
    const start = new Date(startDate);

    for (let i = 0; i < totalPeriods; i++) {
      const periodDate = new Date(start);
      periodDate.setMonth(start.getMonth() + (i * intervalMonths));

      details.push({
        period: i + 1,
        date: periodDate.toISOString().split('T')[0],
        time: bidTime || '12:00',
        displayDate: this.formatDisplayDate(periodDate),
        isEditable: true
      });
    }

    this.setData({ periodDetails: details });
  },

  // 格式化显示日期
  formatDisplayDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 显示期数预览
  showPeriodPreview() {
    if (!this.data.meetingInfo.totalPeriods) {
      wx.showToast({
        title: '请先设置总期数',
        icon: 'none'
      });
      return;
    }

    this.generatePeriodDetails();
    this.setData({ showPeriodModal: true });
  },

  // 关闭期数预览
  closePeriodModal() {
    this.setData({ showPeriodModal: false });
  },

  // 编辑单期日期
  editPeriodDate(e) {
    const index = e.currentTarget.dataset.index;
    const currentDate = this.data.periodDetails[index].date;

    wx.showModal({
      title: `编辑第${index + 1}期日期`,
      editable: true,
      placeholderText: currentDate,
      success: (res) => {
        if (res.confirm && res.content) {
          const newDate = res.content;
          // 验证日期格式
          if (this.isValidDate(newDate)) {
            this.updatePeriodDate(index, newDate);
          } else {
            wx.showToast({
              title: '日期格式不正确',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 验证日期格式
  isValidDate(dateString) {
    const regex = /^\d{4}-\d{2}-\d{2}$/;
    if (!regex.test(dateString)) return false;

    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
  },

  // 更新期数日期
  updatePeriodDate(index, newDate) {
    const periodDetails = [...this.data.periodDetails];
    const newDateObj = new Date(newDate);

    periodDetails[index].date = newDate;
    periodDetails[index].displayDate = this.formatDisplayDate(newDateObj);

    // 更新后续期数日期
    const { intervalMonths } = this.data.meetingInfo;
    for (let i = index + 1; i < periodDetails.length; i++) {
      const nextDate = new Date(newDateObj);
      nextDate.setMonth(newDateObj.getMonth() + ((i - index) * intervalMonths));

      periodDetails[i].date = nextDate.toISOString().split('T')[0];
      periodDetails[i].displayDate = this.formatDisplayDate(nextDate);
    }

    this.setData({ periodDetails });

    wx.showToast({
      title: '日期更新成功',
      icon: 'success'
    });
  },

  // 表单验证
  validateForm() {
    const { meetingInfo } = this.data;
    const errors = {};
    let isValid = true;

    // 验证基本信息
    if (!meetingInfo.title.trim()) {
      errors.title = '请输入会单标题';
      isValid = false;
    }

    if (!meetingInfo.totalPeriods || meetingInfo.totalPeriods <= 0) {
      errors.totalPeriods = '请选择总期数';
      isValid = false;
    }

    if (!meetingInfo.baseAmount || parseFloat(meetingInfo.baseAmount) <= 0) {
      errors.baseAmount = '请输入有效的底标金额';
      isValid = false;
    }

    if (!meetingInfo.startDate) {
      errors.startDate = '请选择开始时间';
      isValid = false;
    }

    if (!meetingInfo.bidTime) {
      errors.bidTime = '请选择投标时间';
      isValid = false;
    }

    this.setData({ errors });
    return isValid;
  },

  // 重置表单
  resetForm() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有表单内容吗？此操作不可撤销。',
      success: (res) => {
        if (res.confirm) {
          // 重置表单数据
          this.setData({
            meetingInfo: {
              title: '',
              totalPeriods: '',
              baseAmount: '',
              startDate: '',
              intervalMonths: 1,
              bidTime: '12:00'
            },
            totalAmount: '',
            periodDetails: [],
            errors: {},
            currentPeriodLabel: '请选择总期数',
            currentIntervalLabel: '1个月'
          });

          // 重新初始化表单（设置默认开始时间）
          this.initializeForm();

          wx.showToast({
            title: '表单已重置',
            icon: 'success'
          });
        }
      }
    });
  },

  // 提交创建
  async submitCreate() {
    if (this.data.isSubmitting) return;

    if (!this.validateForm()) {
      return;
    }

    this.setData({ isSubmitting: true });

    try {
      const app = getApp();
      const userId = await app.getUserId();

      const meetingData = {
        ...this.data.meetingInfo,
        totalAmount: this.data.totalAmount,
        periodDetails: this.data.periodDetails,
        hostId: userId,
        createTime: new Date().toISOString()
      };

      console.log('创建会单数据:', meetingData);
      const { meetingAPI } = require('../../utils/util.js');
      const result = await meetingAPI.createMeeting(meetingData);

      wx.showToast({
        title: '创建成功',
        icon: 'success',
        duration: 2000
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 2000);

    } catch (error) {
      console.error('创建会单失败:', error);
      wx.showToast({
        title: '创建失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ isSubmitting: false });
    }
  }
});
