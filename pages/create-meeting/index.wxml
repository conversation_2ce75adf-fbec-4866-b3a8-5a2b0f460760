<view class="page">
  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{isLoggedIn ? '正在初始化...' : '正在登录中...'}}</text>
  </view>

  <view wx:else class="page-wrapper">
    <!-- 主体内容区域 -->
    <scroll-view scroll-y class="content-container" enhanced="{{true}}" show-scrollbar="{{false}}">
      <!-- 表单区域 -->
      <view class="form-section">
        <!-- 会单标题 -->
        <view class="form-item">
          <view class="label">
            <text class="required">*</text>
            <text>会单标题</text>
          </view>
          <input
            class="input {{errors.title ? 'error' : ''}}"
            type="text"
            placeholder="请输入会单标题"
            placeholder-class="input-placeholder"
            value="{{meetingInfo.title}}"
            bindinput="onTitleInput"
            maxlength="50"
          />
          <view class="error-text" wx:if="{{errors.title}}">{{errors.title}}</view>
        </view>

        <!-- 总期数 -->
        <view class="form-item">
          <view class="label">
            <text class="required">*</text>
            <text>总期数</text>
          </view>
          <picker
            mode="selector"
            range="{{periodOptions}}"
            range-key="label"
            bindchange="onTotalPeriodsChange"
            class="picker {{errors.totalPeriods ? 'error' : ''}}"
          >
            <view class="picker-text">
              {{currentPeriodLabel}}
            </view>
          </picker>
          <view class="error-text" wx:if="{{errors.totalPeriods}}">{{errors.totalPeriods}}</view>
        </view>

        <!-- 底标金额 -->
        <view class="form-item">
          <view class="label">
            <text class="required">*</text>
            <text>底标金额</text>
          </view>
          <input
            class="input {{errors.baseAmount ? 'error' : ''}}"
            type="digit"
            placeholder="请输入底标金额"
            placeholder-class="input-placeholder"
            value="{{meetingInfo.baseAmount}}"
            bindinput="onBaseAmountInput"
          />
          <view class="error-text" wx:if="{{errors.baseAmount}}">{{errors.baseAmount}}</view>
        </view>

        <!-- 总金额展示 -->
        <view class="total-amount-display" wx:if="{{totalAmount}}">
          <view class="total-label">总金额</view>
          <view class="total-value">¥{{totalAmount}}</view>
          <view class="total-formula">{{meetingInfo.totalPeriods}}期 × ¥{{meetingInfo.baseAmount}}</view>
        </view>

        <!-- 开始时间 -->
        <view class="form-item">
          <view class="label">
            <text class="required">*</text>
            <text>开始时间</text>
          </view>
          <picker
            mode="date"
            value="{{meetingInfo.startDate}}"
            bindchange="onStartDateChange"
            class="picker {{errors.startDate ? 'error' : ''}}"
          >
            <view class="picker-text">
              {{meetingInfo.startDate || '请选择开始时间'}}
            </view>
          </picker>
          <view class="error-text" wx:if="{{errors.startDate}}">{{errors.startDate}}</view>
        </view>

        <!-- 间隔月份 -->
        <view class="form-item">
          <view class="label">
            <text class="required">*</text>
            <text>间隔月份</text>
          </view>
          <picker
            mode="selector"
            range="{{intervalOptions}}"
            range-key="label"
            bindchange="onIntervalMonthsChange"
            class="picker"
          >
            <view class="picker-text">{{currentIntervalLabel}}</view>
          </picker>
        </view>

        <!-- 投标时间 -->
        <view class="form-item">
          <view class="label">
            <text class="required">*</text>
            <text>投标时间</text>
          </view>
          <picker
            mode="time"
            value="{{meetingInfo.bidTime}}"
            bindchange="onBidTimeChange"
            class="picker {{errors.bidTime ? 'error' : ''}}"
          >
            <view class="picker-text">
              {{meetingInfo.bidTime || '请选择投标时间'}}
            </view>
          </picker>
          <view class="error-text" wx:if="{{errors.bidTime}}">{{errors.bidTime}}</view>
        </view>

        <!-- 查看期数详情按钮 -->
        <view class="period-preview-btn" bindtap="showPeriodPreview">
          <text class="preview-text">查看期数详情</text>
          <text class="preview-arrow">›</text>
        </view>
      </view>

    </scroll-view>

    <!-- 底部按钮区 -->
    <view class="footer-buttons">
      <button
        class="reset-btn"
        bindtap="resetForm"
      >
        <text>重置</text>
      </button>
      <button
        class="create-btn {{isSubmitting ? 'loading' : ''}}"
        bindtap="submitCreate"
        disabled="{{isSubmitting}}"
      >
        <text wx:if="{{!isSubmitting}}">创建会单</text>
        <text wx:else>创建中...</text>
      </button>
    </view>
  </view>

  <!-- 期数预览弹窗 -->
  <view class="period-modal {{showPeriodModal ? 'show' : ''}}" wx:if="{{showPeriodModal}}">
    <view class="modal-mask" bindtap="closePeriodModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">期数详情预览</text>
        <view class="modal-close" bindtap="closePeriodModal">×</view>
      </view>

      <scroll-view scroll-y class="modal-body">
        <view wx:for="{{periodDetails}}" wx:key="period" class="period-item">
          <view class="period-info">
            <view class="period-number">第{{item.period}}期</view>
            <view class="period-datetime">
              <text class="period-date">{{item.displayDate}}</text>
              <text class="period-time">{{item.time}}</text>
            </view>
          </view>
          <view class="period-edit" bindtap="editPeriodDate" data-index="{{index}}">
            <text class="edit-text">编辑</text>
          </view>
        </view>
      </scroll-view>

      <view class="modal-footer">
        <button class="confirm-btn" bindtap="closePeriodModal">确认</button>
      </view>
    </view>
  </view>
</view>
