const { meetingAPI, formatMoney } = require('../../utils/util.js');

Page({
  data: {
    showNumericKeyboard: false,
    inputAmount: '',
    currentBidAmount: '',
    meetingInfo: {},
    showMoreActions: false,
    showAddMemberPopup: false,
    showRecordPaymentPopup: false,
    newMember: {},
    newTransaction: {},
    memberNames: ['李先生', '王女士'],
    isLoading: true,
    isLoggedIn: false,
    isInviteShare: false, // 标识是否是邀请会员的分享
    isSubscribed: false, // 是否已订阅提醒
    countdownText: '计算中...', // 倒计时显示文本
    countdownTimer: null, // 倒计时定时器
    // 图表数据
    chartData: {
      income: 30000,    // 已收款
      expense: 30000,   // 已付款
      pending: 70000    // 待收款
    },
    record: [],
    stats: []
  },

  onLoad(options) {
    console.log('详情页 onLoad 开始');
    const { id } = options;
    this.meetingId = id;
    this.loginAndLoadData();
    console.log('详情页 onLoad 完成');
  },

  onUnload() {
    // 页面卸载时清除定时器
    this.clearCountdownTimer();
  },

  onHide() {
    // 页面隐藏时清除定时器
    this.clearCountdownTimer();
  },

  onShow() {
    // 页面显示时重新启动倒计时
    if (this.data.meetingInfo.current_period) {
      this.startCountdown();
    }
  },

  onReady() {
    // 页面渲染完成后延迟初始化图表
    setTimeout(() => {
      this.drawChart();
    }, 500);
  },

  // 绘制原生Canvas图表
  drawChart() {
    const query = wx.createSelectorQuery().in(this);
    query.select('#statsChart')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res || !res[0] || !res[0].node) {
          console.error('Canvas节点获取失败');
          return;
        }

        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          console.error('无法获取Canvas上下文');
          return;
        }

        const dpr = wx.getSystemInfoSync().pixelRatio;
        canvas.width = res[0].width * dpr;
        canvas.height = res[0].height * dpr;
        ctx.scale(dpr, dpr);

        this.renderPieChart(ctx, res[0].width, res[0].height);
      });
  },

  // 渲染饼图
  renderPieChart(ctx, width, height) {
    const { chartData } = this.data;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 3;
    const innerRadius = radius * 0.6;

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    // 计算总值
    const total = chartData.income + chartData.expense + chartData.pending;

    if (total === 0) return;

    // 颜色配置
    const colors = ['#4CAF50', '#FF5252', '#FF9800'];
    const data = [
      { value: chartData.income, name: '已收款' },
      { value: chartData.expense, name: '已付款' },
      { value: chartData.pending, name: '待收款' }
    ];

    let currentAngle = -Math.PI / 2; // 从顶部开始

    // 绘制环形图
    data.forEach((item, index) => {
      if (item.value > 0) {
        const angle = (item.value / total) * 2 * Math.PI;

        // 绘制扇形
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + angle);
        ctx.arc(centerX, centerY, innerRadius, currentAngle + angle, currentAngle, true);
        ctx.closePath();
        ctx.fillStyle = colors[index];
        ctx.fill();

        currentAngle += angle;
      }
    });

    // 绘制中心文字
    ctx.fillStyle = '#666666';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('总金额', centerX, centerY - 10);

    ctx.fillStyle = '#333333';
    ctx.font = 'bold 16px sans-serif';
    ctx.fillText(`¥${formatMoney(total)}`, centerX, centerY + 10);
  },

  // 图表点击事件
  onChartTouchStart(e) {
    const { chartData } = this.data;
    const total = chartData.income + chartData.expense + chartData.pending;

    const incomePercent = ((chartData.income / total) * 100).toFixed(1);
    const expensePercent = ((chartData.expense / total) * 100).toFixed(1);
    const pendingPercent = ((chartData.pending / total) * 100).toFixed(1);

    wx.showModal({
      title: '资金统计详情',
      content: `已收款：¥${formatMoney(chartData.income)} (${incomePercent}%)\n已付款：¥${formatMoney(chartData.expense)} (${expensePercent}%)\n待收款：¥${formatMoney(chartData.pending)} (${pendingPercent}%)`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 登录并加载数据
  async loginAndLoadData() {
    console.log('详情页开始登录并加载数据');
    this.setData({
      isLoading: true,
      isLoggedIn: false
    });

    try {
      const app = getApp();
      console.log('调用 app.ensureLogin()');
      const userInfo = await app.ensureLogin();
      console.log('登录成功，用户信息:', userInfo);

      this.setData({ isLoggedIn: true });
      await this.getMeetingDetail(this.meetingId);
      this.setData({ isLoading: false });
    } catch (error) {
      console.error('登录失败:', error);
      this.setData({ isLoading: false });
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    }
  },

  async getMeetingDetail(id) {
    if (!this.data.isLoggedIn) {
      console.log('用户未登录，跳过数据加载');
      return;
    }

    try {
      console.log('获取会单详情:', id);
      const result = await meetingAPI.getMeetingDetail(id);
      if (result.success) {
        this.setData({
          meetingInfo: result.data.info,
          record: result.data.record,
          stats: result.data.stats,
          currentBidAmount: result.data.info?.current_period?.formatted_bid_price || ''
        });

        // 启动倒计时
        if (result.data.info?.current_period) {
          this.startCountdown();
        }
      }
    } catch (error) {
      console.error('获取会单详情失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },
  showMoreActions() {
    this.setData({
      showMoreActions: true
    });
  },

  async showBidModal() {
    // 检查是否禁用投标
    if (this.data.meetingInfo.bid_disable) {
      wx.showToast({
        title: '您已中标，无法再次投标',
        icon: 'none'
      });
      return;
    }

    // 检查是否是会东
    if (this.data.meetingInfo.is_creator) {
      wx.showToast({
        title: '会东无需投标',
        icon: 'none'
      });
      return;
    }

    // 检查用户信息是否完整
    const app = getApp();
    const canProceed = await app.checkAndCompleteUserInfo('bid');

    if (canProceed) {
      this.setData({
        showNumericKeyboard: true,
        inputAmount: ''
      });
    }
  },

  closeNumericKeyboard() {
    this.setData({
      showNumericKeyboard: false
    });
  },

  appendDigit(e) {
    const digit = e.currentTarget.dataset.digit;
    let { inputAmount } = this.data;

    // 触发短振动
    wx.vibrateShort({
      type: 'light'
    });

    // 限制最大长度为8位
    if (inputAmount.length >= 8) return;

    // 如果当前输入为'0'，则替换为新数字
    if (inputAmount === '0') {
      inputAmount = digit;
    } else {
      inputAmount += digit;
    }

    this.setData({
      inputAmount
    });
  },

  deleteDigit() {
    // 触发短振动
    wx.vibrateShort({
      type: 'light'
    });

    let { inputAmount } = this.data;
    if (inputAmount.length > 0) {
      inputAmount = inputAmount.slice(0, -1);
      this.setData({
        inputAmount
      });
    }
  },

  async confirmBid() {
    // 触发较重的振动
    wx.vibrateShort({
      type: 'medium'
    });

    const { inputAmount, meetingInfo: { current_period: { id: periodId } } } = this.data;
    if (!inputAmount || inputAmount === '0') {
      wx.showToast({
        title: '请输入投标金额',
        icon: 'none'
      });
      return;
    }
    if (periodId == null) {
      wx.showToast({
        title: '当前无有效会期',
        icon: 'none'
      });
      return;
    }

    const result = await meetingAPI.bidMeeting(this.meetingId, periodId, inputAmount);
    if (!result.success) {
      wx.showToast({
        title: result.message || '投标失败',
        icon: 'none'
      });
      return;
    }

    // 更新当前投标金额
    this.setData({
      currentBidAmount: formatMoney(inputAmount),
      showNumericKeyboard: false
    });

    wx.showToast({
      title: '投标成功',
      icon: 'success'
    });
  },

  // 切换订阅状态
  toggleSubscribe() {
    const newSubscribeState = !this.data.isSubscribed;
    this.setData({
      isSubscribed: newSubscribeState
    });

    wx.showToast({
      title: newSubscribeState ? '订阅成功' : '取消订阅',
      icon: 'success'
    });

    // TODO: 调用订阅/取消订阅接口
    console.log('订阅状态:', newSubscribeState);
  },

  // 启动倒计时
  startCountdown() {
    const { meetingInfo } = this.data;
    if (!meetingInfo.current_period || !meetingInfo.current_period.s_date || !meetingInfo.bid_time) {
      this.setData({ countdownText: '时间待定' });
      return;
    }

    // 清除之前的定时器
    this.clearCountdownTimer();

    // 计算目标时间
    const targetDateTime = `${meetingInfo.current_period.s_date} ${meetingInfo.bid_time}`;
    const targetTime = new Date(targetDateTime).getTime();

    if (isNaN(targetTime)) {
      this.setData({ countdownText: '时间格式错误' });
      return;
    }

    // 立即执行一次
    this.updateCountdown(targetTime);

    // 设置定时器，每秒更新
    const timer = setInterval(() => {
      this.updateCountdown(targetTime);
    }, 1000);

    this.setData({ countdownTimer: timer });
  },

  // 更新倒计时显示
  updateCountdown(targetTime) {
    const now = new Date().getTime();
    const timeDiff = targetTime - now;

    if (timeDiff <= 0) {
      this.setData({ countdownText: '已开始' });
      this.clearCountdownTimer();
      return;
    }

    // 计算天、时、分、秒
    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

    // 格式化显示文本
    let countdownText = '';
    if (days > 0) {
      countdownText += `${days}天`;
    }
    if (hours > 0 || days > 0) {
      countdownText += `${hours}小时`;
    }
    if (minutes > 0 || hours > 0 || days > 0) {
      countdownText += `${minutes}分`;
    }
    countdownText += `${seconds}秒`;

    this.setData({ countdownText });
  },

  // 清除倒计时定时器
  clearCountdownTimer() {
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
      this.setData({ countdownTimer: null });
    }
  },

  closeMoreActions() {
    this.setData({
      showMoreActions: false
    });
  },

  editMeeting() {
    wx.navigateTo({
      url: `/pages/edit-meeting/index?id=${this.data.meetingInfo.id}`
    });
  },

  deleteMeeting() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个会单吗？删除后无法恢复。',
      success: (res) => {
        if (res.confirm) {
          // TODO: 调用删除接口
          console.log('删除会单');
        }
      }
    });
  },

  shareMeeting() {
    // TODO: 实现分享逻辑
    console.log('分享会单');
    this.closeMoreActions();
  },

  exportData() {
    // TODO: 实现导出数据逻辑
    console.log('导出数据');
    this.closeMoreActions();
  },

  archiveMeeting() {
    // TODO: 实现归档逻辑
    console.log('归档会单');
    this.closeMoreActions();
  },

  contactHost() {
    wx.makePhoneCall({
      phoneNumber: this.data.meetingInfo.host_info.user.phone.replace(/\*/g, ''),
      fail: () => {
        wx.showToast({
          title: '拨号失败',
          icon: 'none'
        });
      }
    });
  },

  showAddMemberModal() {
    this.setData({
      showAddMemberPopup: true,
      newMember: {
        name: '',
        contact: '',
        role: '普通会员'
      }
    });
  },

  closeAddMemberModal() {
    this.setData({
      showAddMemberPopup: false
    });
  },

  setMemberRole(e) {
    const role = e.currentTarget.dataset.role;
    this.setData({
      'newMember.role': role
    });
  },

  confirmAddMember() {
    const {
      newMember
    } = this.data;
    if (!newMember.name || !newMember.contact) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }
    // TODO: 调用添加会员接口
    console.log('添加会员:', newMember);
    this.closeAddMemberModal();
  },

  showMemberDetail(e) {
    console.log('e', e)
    const member = e.currentTarget.dataset.member;
    console.log('member', member)
    wx.showModal({
      title: '会员信息',
      content: `姓名：${member.name}\n加入时间：${member.joinDate}\n状态：${member.status}`,
      showCancel: false
    });
  },

  viewAllPeriods() {
    wx.navigateTo({
      url: `/pages/period-list/index?meetingId=${this.data.meetingInfo.id || this.meetingId}`
    });
  },

  viewAllTransactions() {
    wx.navigateTo({
      url: '/pages/transaction-list/index'
    });
  },

  showPeriodDetail(e) {
    const period = e.currentTarget.dataset.period;
    wx.navigateTo({
      url: `/pages/period-detail/index?number=${period.number}`
    });
  },

  showRecordPaymentModal() {
    this.setData({
      showRecordPaymentPopup: true,
      newTransaction: {
        type: '收款',
        amount: '',
        member: '',
        remark: ''
      }
    });
  },

  closeRecordPaymentModal() {
    this.setData({
      showRecordPaymentPopup: false
    });
  },

  setTransactionType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      'newTransaction.type': type
    });
  },

  onMemberPickerChange(e) {
    const index = e.detail.value;
    this.setData({
      'newTransaction.member': this.data.memberNames[index]
    });
  },

  confirmRecordPayment() {
    const {
      newTransaction
    } = this.data;
    if (!newTransaction.amount || !newTransaction.member) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }
    // TODO: 调用记录收付款接口
    console.log('记录收付款:', newTransaction);
    this.closeRecordPaymentModal();
  },

  // 设置邀请分享标识
  setInviteShare() {
    this.setData({
      isInviteShare: true
    });
  },

  onShareAppMessage() {
    // 检查是否是邀请会员的分享
    if (this.data.isInviteShare) {
      // 重置分享标识
      this.setData({
        isInviteShare: false
      });

      return {
        title: `邀请您加入「${this.data.meetingInfo.title || this.data.meetingInfo.name}」`,
        path: `/pages/join-meeting/index?meetingId=${this.data.meetingInfo.id || this.meetingId}`,
        success: (res) => {
          console.log('邀请分享成功', res);
          wx.showToast({
            title: '邀请已发送',
            icon: 'success'
          });
        }
      };
    }

    // 默认分享（查看会单详情）
    return {
      title: this.data.meetingInfo.title || this.data.meetingInfo.name,
      path: `/pages/detail/detail?id=${this.data.meetingInfo.id || this.meetingId}`
    };
  },
  // 会员名称列表（用于选择器）
  memberNames() {
    return this.data.memberList.map((member) => member.name);
  },
  // 根据会期状态获取样式类
  getPeriodStatusClass(status) {
    if (status === "已完成") return "status-completed";
    if (status === "进行中") return "status-ongoing";
    return "status-pending";
  },
  // 编辑会单
  editMeeting() {
    wx.navigateTo({
      url: `/pages/edit-meeting/index?id=${this.data.meetingInfo.id}`,
    });
  },
  // 分享会单
  shareMeeting() {
    wx.showToast({
      title: "分享功能开发中",
      icon: "none",
    });
    this.closeMoreActions();
  },
  // 导出数据
  exportData() {
    wx.showToast({
      title: "导出功能开发中",
      icon: "none",
    });
    this.closeMoreActions();
  },
  // 归档会单
  archiveMeeting() {
    wx.showModal({
      title: "归档会单",
      content: "确定要归档该会单吗？归档后会单将不再显示在主列表中。",
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: "归档成功",
            icon: "success",
          });
        }
      },
    });
    this.closeMoreActions();
  },
  // 删除会单
  deleteMeeting() {
    wx.showModal({
      title: "删除会单",
      content: "确定要删除该会单吗？删除后无法恢复。",
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: "删除成功",
            icon: "success",
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      },
    });
    this.closeMoreActions();
  },
  // 显示会期详情
  showPeriodDetail(period) {
    wx.showToast({
      title: `查看第${period.number}期详情`,
      icon: "none",
    });
  },
  // 跳转到会期页面
  navigateToPeriods() {
    wx.navigateTo({
      url: `/pages/period-list/index?id=${this.data.meetingInfo.id}`,
    });
  },
  // 跳转到交易记录页面
  navigateToTransactions() {
    wx.navigateTo({
      url: `/pages/transaction-list/index?id=${this.data.meetingInfo.id}`,
    });
  },
})