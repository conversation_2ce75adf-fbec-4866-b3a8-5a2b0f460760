page {
  height: 100%;
}
.page {
  height: 100vh;
  background-color: #F5F5F5;
  position: relative;
  overflow: hidden;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3b7cf7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.page-wrapper {
  height: 100%;
  perspective: 1500rpx;
  perspective-origin: center bottom;
  overflow: hidden;
}

.content-container {
  height: 100%;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
  will-change: transform;
}

.content-pushed {
  transform-origin: center bottom;
  transform: rotateX(2deg) translate3d(0, -20rpx, -30rpx);
}
/* 顶部导航栏 */
.header {
  height: 100rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}
.back-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}
.action-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* 内容区域 */
.content-container {
  flex: 1;
  padding: 20rpx 30rpx;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  overflow: auto;
}
/* 会单基本信息卡片 */
.meeting-info-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}
.meeting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.meeting-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}
.status-tag {
  font-size: 12px;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  background-color: #e6f0ff;
  color: #3b7cf7;
}
.meeting-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.stat-label {
  font-size: 12px;
  color: #999999;
}
.progress-section {
  border-top: 1px solid #eeeeee;
  padding-top: 20rpx;
}
.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.progress-title {
  font-size: 14px;
  color: #666666;
}
.progress-text {
  font-size: 14px;
  color: #3b7cf7;
  font-weight: 500;
}
.progress-bar {
  height: 8px;
  background-color: #eeeeee;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 16rpx;
}
.progress-fill {
  height: 100%;
  background-color: #3b7cf7;
  border-radius: 4px;
}
.next-period-date {
  font-size: 12px;
  color: #999999;
}
/* 通用部分卡片样式 */
.section-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}
.view-all-btn {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.view-all-btn text {
  color: #3b7cf7;
  text-decoration: none;
}

.icon-small {
  width: 18px;
  height: 18px;
}
/* 会东信息 */
.host-info {
  display: flex;
  align-items: center;
}
.host-avatar {
  margin-right: 20rpx;
}
.host-details {
  flex: 1;
}
.host-name {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}
.host-contact {
  font-size: 14px;
  color: #666666;
  margin-bottom: 16rpx;
  display: block;
}
.host-stats {
  display: flex;
}
.host-stat-item {
  display: flex;
  flex-direction: column;
  margin-right: 40rpx;
}
.host-stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #3b7cf7;
  margin-bottom: 4rpx;
  display: block;
}
.host-stat-label {
  font-size: 12px;
  color: #999999;
  display: block;
}
.contact-host-btn {
  width: 160rpx;
  height: 70rpx;
  line-height: 70rpx;
  background-color: #3b7cf7;
  color: #ffffff;
  font-size: 14px;
  border-radius: 35rpx;
  text-align: center;
  padding: 0;
  margin: 0;
  flex-shrink: 0;
}
/* 会员列表 */
.add-member-btn {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #3b7cf7;
}
.add-member-btn text {
  margin-left: 4rpx;
}

/* 邀请会员分享按钮样式重置 */
.invite-share-btn {
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
  line-height: normal;
  border-radius: 0;
}

.invite-share-btn::after {
  border: none;
}
.members-scroll {
  width: 100%;
  white-space: nowrap;
}
.members-container {
  display: inline-flex;
  padding: 10rpx 0;
}
.member-card {
  width: 160rpx;
  margin-right: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.member-avatar {
  margin-bottom: 16rpx;
}
.member-name {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160rpx;
  text-align: center;
}
.member-join-date {
  font-size: 12px;
  color: #999999;
  margin-bottom: 8rpx;
  white-space: nowrap;
}
.member-status {
  font-size: 12px;
  color: #3b7cf7;
  white-space: nowrap;
}
.status-inactive {
  color: #ff5252;
}
/* 会期详情表格 */
.periods-table {
  border: 1px solid #eeeeee;
  border-radius: 8rpx;
  overflow: hidden;
}
.table-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #eeeeee;
  padding: 16rpx 0;
}
.table-row {
  display: flex;
  border-bottom: 1px solid #eeeeee;
  padding: 20rpx 0;
}
.table-row:last-child {
  border-bottom: none;
}
.table-cell {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #666666;
  padding: 0 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.table-header .table-cell {
  font-weight: 600;
  color: #333333;
}
.period-num {
  flex: 0.7;
}
.period-date {
  flex: 1.5;
}
.period-amount {
  flex: 1;
}
.period-member {
  flex: 0.9;
}
.period-status {
  flex: 1;
}
.status-pill {
  display: inline-block;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  font-size: 12px;
}
.status-completed {
  background-color: #e8f5e9;
  color: #4caf50;
}
.status-ongoing {
  background-color: #e6f0ff;
  color: #3b7cf7;
}
.status-pending {
  background-color: #f5f5f5;
  color: #999999;
}
/* 收付款记录时间轴 */
.transaction-timeline {
  position: relative;
  padding-left: 30rpx;
}
.transaction-timeline::before {
  content: "";
  position: absolute;
  left: 10rpx;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #eeeeee;
}
.transaction-item {
  position: relative;
  margin-bottom: 30rpx;
}
.transaction-item:last-child {
  margin-bottom: 0;
}
.timeline-dot {
  position: absolute;
  left: -30rpx;
  top: 16rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 10rpx;
  background-color: #3b7cf7;
  z-index: 1;
}
.dot-income {
  background-color: #4caf50;
}
.dot-expense {
  background-color: #ff5252;
}
.transaction-content {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
}
.transaction-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}
.transaction-date {
  font-size: 14px;
  color: #666666;
}
.transaction-status {
  font-size: 12px;
  color: #4caf50;
}
.status-pending {
  color: #ff9800;
}
.transaction-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}
.transaction-type {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}
.transaction-amount {
  font-size: 16px;
  font-weight: 600;
}
.amount-income {
  color: #4caf50;
}
.amount-expense {
  color: #ff5252;
}
.transaction-operator {
  font-size: 12px;
  color: #999999;
}
/* 资金统计 */
.stats-summary {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}
.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stats-label {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8rpx;
}
.stats-value {
  font-size: 18px;
  font-weight: 600;
}
.income-color {
  color: #4caf50;
}
.expense-color {
  color: #ff5252;
}
.pending-color {
  color: #ff9800;
}
.stats-chart {
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #fafafa;
  padding: 20rpx;
}

.chart-canvas {
  width: 100%;
  height: 300rpx;
  background-color: white;
  border-radius: 8rpx;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 40rpx;
  margin-top: 20rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.legend-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
}

.income-dot {
  background-color: #4CAF50;
}

.expense-dot {
  background-color: #FF5252;
}

.pending-dot {
  background-color: #FF9800;
}

.legend-text {
  font-size: 24rpx;
  color: #666;
}
/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background-color: #ffffff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 100;
}
.action-button {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 14px;
  margin: 0 10rpx;
}
.action-button text {
  margin-left: 8rpx;
}
.edit-btn {
  background-color: #3b7cf7;
  color: #ffffff;
  border: none;
}
.add-member-btn {
  background-color: #ff9800;
  color: #ffffff;
  border: none;
}
.record-payment-btn {
  background-color: #4caf50;
  color: #ffffff;
  border: none;
}
/* 自定义数字键盘 */
.numeric-keyboard {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  transform: translate3d(0, 100%, 0);
  transition: transform .3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
  box-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.numeric-keyboard.visible {
  transform: translate3d(0, 0, 0);
}

.keyboard-header {
  padding: 30rpx;
  border-bottom: 1px solid #eeeeee;
}

.keyboard-title {
  font-size: 16px;
  color: #333333;
  font-weight: 600;
  margin-bottom: 20rpx;
  display: block;
}

.current-bid {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.bid-label {
  font-size: 14px;
  color: #666666;
}

.bid-amount {
  font-size: 14px;
  color: #3b7cf7;
  font-weight: 500;
}

.input-display {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  background-color: #f5f7fa;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.currency-symbol {
  font-size: 24px;
  color: #333333;
  margin-right: 8rpx;
}

.input-value {
  font-size: 32px;
  color: #333333;
  font-weight: 600;
}

.keyboard-grid {
  padding: 20rpx;
}

.key-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.key-row:last-child {
  margin-bottom: 0;
}

.key-btn {
  flex: 1;
  height: 100rpx;
  margin: 0 10rpx;
  background-color: #f5f7fa;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #333333;
  font-weight: 500;
}

.key-btn:active {
  background-color: #e6e6e6;
}

.cancel-btn {
  color: #666666;
}

.delete-btn {
  color: #ff5252;
}

.keyboard-footer {
  padding: 20rpx 30rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  background-color: #3b7cf7;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.confirm-btn:active {
  background-color: #2f63c6;
}

/* 弹窗遮罩层 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
}

/* 底部弹窗 */
.popup-content {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  z-index: 1001;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 32rpx;
}

.popup-actions {
  margin-bottom: 32rpx;
}

.popup-action-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
}

.popup-action-item .icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}

.popup-action-item text {
  font-size: 28rpx;
  color: #333333;
}

.delete-action text {
  color: #ff4d4f;
}

.popup-cancel {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 32rpx;
  color: #999999;
  background-color: #f5f5f5;
  border-radius: 44rpx;
}

/* 表单弹窗 */
.form-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  z-index: 1001;
}

.form-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-popup-title {
  font-size: 32rpx;
  font-weight: bold;
}

.form-popup-content {
  padding: 24rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 12rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 8rpx;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 8rpx;
  box-sizing: border-box;
}

.radio-group {
  display: flex;
  gap: 32rpx;
}

.radio-item {
  display: flex;
  align-items: center;
}

.radio-dot {
  width: 32rpx;
  height: 32rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 50%;
  margin-right: 8rpx;
  position: relative;
}

.radio-active .radio-dot::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16rpx;
  height: 16rpx;
  background-color: #1890ff;
  border-radius: 50%;
}

.form-picker {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 8rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.picker-value {
  color: #333333;
}

.form-popup-footer {
  display: flex;
  justify-content: space-between;
  padding: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.form-btn {
  width: 260rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 40rpx;
}

.cancel-btn {
  color: #999999;
  background-color: #f5f5f5;
}

.confirm-btn {
  color: #ffffff;
  background-color: #1890ff;
}
.cursor-pointer {
  cursor: pointer;
}

/* 即将到期会期卡片 */
.upcoming-period-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.upcoming-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.upcoming-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.countdown-container {
  display: flex;
  align-items: center;
}

.countdown-label {
  font-size: 12px;
  color: #666666;
  margin-right: 8rpx;
}

.countdown-value {
  font-size: 14px;
  color: #ff5252;
  font-weight: 500;
}

.upcoming-content {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
}

.period-info {
  margin-bottom: 20rpx;
}

.period-label {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-right: 16rpx;
}

.period-date {
  font-size: 14px;
  color: #666666;
  margin-right: 16rpx;
}

.period-amount {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.bid-info {
  border-top: 1px solid #eeeeee;
  padding-top: 20rpx;
}

.bid-progress {
  margin-bottom: 20rpx;
}

.bid-label {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8rpx;
  display: block;
}

.bid-amount {
  font-size: 18px;
  font-weight: 600;
  color: #3b7cf7;
  margin-bottom: 8rpx;
  display: block;
}

.bid-progress-bar {
  height: 8px;
  background-color: #eeeeee;
  border-radius: 4px;
  overflow: hidden;
}

.bid-actions {
  display: flex;
  align-items: center;
}

.bid-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #3b7cf7;
  color: #ffffff;
  border-radius: 40rpx;
  font-size: 14px;
  margin-right: 20rpx;
  border: none;
}

.bid-btn text {
  margin-left: 8rpx;
}

.bid-btn image {
  width: 16px;
  height: 16px;
}

/* 投标按钮禁用状态 */
.bid-btn-disabled {
  background-color: #f5f5f5 !important;
  color: #cccccc !important;
  cursor: not-allowed !important;
}

.bid-btn-disabled image {
  opacity: 0.5;
}

.subscribe-btn {
  width: 200rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e8f0fe;
  border-radius: 40rpx;
  font-size: 14px;
  color: #5a6c7d;
  font-weight: 500;
}

.subscribe-btn image {
  width: 16px;
  height: 16px;
}

.subscribe-btn text {
  margin-left: 8rpx;
}

/* 已订阅状态的小订阅按钮 */
.subscribe-btn.subscribed {
  background-color: #e6f0ff;
  color: #3b7cf7;
}

/* 会东状态和B状态下的操作区域样式调整 */
.creator-actions {
  justify-content: center;
}

/* 全宽订阅按钮样式 */
.subscribe-btn-full {
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e8f0fe;
  border-radius: 40rpx;
  font-size: 14px;
  color: #5a6c7d;
  font-weight: 500;
}

.subscribe-btn-full image {
  width: 16px;
  height: 16px;
}

.subscribe-btn-full text {
  margin-left: 8rpx;
}

/* 已订阅状态的全宽按钮 */
.subscribe-btn-full.subscribed {
  background-color: #e6f0ff;
  color: #3b7cf7;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 24rpx;
  color: #CCCCCC;
  line-height: 1.4;
}

/* 会员信息弹窗 */
.member-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.member-popup-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  pointer-events: none;
}

.member-popup-container.show {
  pointer-events: auto;
}

.member-popup {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  transform: scale(0.7);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.member-popup.popup-show {
  transform: scale(1);
  opacity: 1;
}

.member-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #eeeeee;
}

.member-popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-icon {
  width: 32rpx;
  height: 32rpx;
}

.member-popup-content {
  padding: 30rpx;
}

.member-popup-profile {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.member-popup-avatar {
  margin-right: 20rpx;
}

.member-popup-info {
  flex: 1;
}

.member-popup-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.member-popup-contact,
.member-popup-join-date,
.member-popup-status {
  font-size: 14px;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.member-popup-actions {
  display: flex;
  padding: 30rpx;
  padding-top: 0;
}

.member-popup-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 14px;
  border: none;
  background-color: #3b7cf7;
  color: #ffffff;
}

.member-popup-btn:active {
  background-color: #2563eb;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}