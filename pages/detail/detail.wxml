<view class="page">
  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{isLoggedIn ? '正在加载会单详情...' : '正在登录中...'}}</text>
  </view>

  <view wx:else class="page-wrapper">
    <!-- 主体内容区域 -->
    <scroll-view scroll-y class="content-container{{showNumericKeyboard ? ' content-pushed' : ''}}" enhanced="{{true}}" show-scrollbar="{{false}}">
    <!-- 会单基本信息 -->
    <view class="meeting-info-card">
      <view class="meeting-header">
        <view class="meeting-title">{{meetingInfo.title}}</view>
        <view class="status-tag">{{meetingInfo.state_txt}}</view>
      </view>
      <view class="meeting-stats">
        <view class="stat-item">
          <text class="stat-value">¥{{meetingInfo.formatted_total_amount}}</text>
          <text class="stat-label">总金额</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{meetingInfo.period_num}}</text>
          <text class="stat-label">会期</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{meetingInfo.member_list.length || 0}} / {{meetingInfo.period_num}}</text>
          <text class="stat-label">会员数</text>
        </view>
      </view>
      <view class="progress-section">
        <view class="progress-header">
          <text class="progress-title">会期进度</text>
          <text class="progress-text">{{meetingInfo.current_period ? meetingInfo.current_period.number - 1 : 0}} / {{meetingInfo.period_num}}期</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{meetingInfo.periods_count / meetingInfo.period_num * 100}}%"></view>
        </view>
        <text class="next-period-date">下期时间：{{meetingInfo.current_period.s_date || '-'}}</text>
      </view>
    </view>
    <!-- 即将到期会期提醒 -->
    <view class="upcoming-period-card" wx:if="{{meetingInfo.current_period && meetingInfo.state !== 'finish'}}">
      <view class="upcoming-header">
        <text class="upcoming-title">即将开始</text>
        <view class="countdown-container">
          <text class="countdown-label">距离开始</text>
          <text class="countdown-value">18天12小时35分</text>
        </view>
      </view>
      <view class="upcoming-content">
        <view class="period-info">
          <text class="period-label">第{{meetingInfo.current_period.number}}期</text>
          <text class="period-date">{{meetingInfo.current_period.s_date}} {{meetingInfo.bid_time}}</text>
          <text class="period-amount">标的金额：¥{{meetingInfo.formatted_base_amount}}</text>
        </view>
        <!-- 会东状态（A状态）：不显示投标相关内容，只显示订阅按钮 -->
        <view class="bid-info" wx:if="{{meetingInfo.is_creator}}">
          <view class="bid-actions creator-actions">
            <view class="subscribe-btn-full {{isSubscribed ? 'subscribed' : ''}} cursor-pointer" bindtap="toggleSubscribe">
              <image src="/static/images/remind.svg"></image>
              <text>{{isSubscribed ? '已订阅' : '订阅提醒'}}</text>
            </view>
          </view>
        </view>
        <!-- 会员状态（B状态和C状态）：显示投标相关内容 -->
        <view class="bid-info" wx:if="{{!meetingInfo.is_creator}}">
          <!-- C状态：普通会员，显示已投标金额 -->
          <view class="bid-progress" wx:if="{{!meetingInfo.bid_disable}}">
            <text class="bid-label">已投标金额</text>
            <text class="bid-amount">¥{{currentBidAmount}}</text>
          </view>
          <!-- C状态：显示投标按钮和订阅按钮 -->
          <view class="bid-actions" wx:if="{{!meetingInfo.bid_disable}}">
            <view class="bid-btn cursor-pointer" bindtap="showBidModal">
              <image src="/static/images/money-rmb.svg"></image>
              <text>立即投标</text>
            </view>
            <view class="subscribe-btn {{isSubscribed ? 'subscribed' : ''}} cursor-pointer" bindtap="toggleSubscribe">
              <image src="/static/images/remind.svg"></image>
              <text>{{isSubscribed ? '已订阅' : '订阅提醒'}}</text>
            </view>
          </view>
          <!-- B状态：已中标会员，只显示订阅按钮 -->
          <view class="bid-actions creator-actions" wx:if="{{meetingInfo.bid_disable}}">
            <view class="subscribe-btn-full {{isSubscribed ? 'subscribed' : ''}} cursor-pointer" bindtap="toggleSubscribe">
              <image src="/static/images/remind.svg"></image>
              <text>{{isSubscribed ? '已订阅' : '订阅提醒'}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 会东信息 -->
    <view class="section-card">
      <view class="section-header">
        <text class="section-title">会东信息</text>
      </view>
      <view class="host-info">
        <default-avatar
          src="{{meetingInfo.host_info.user.avatar}}"
          name="{{meetingInfo.host_info.user.name}}"
          size="medium"
          custom-class="host-avatar"
          style="margin-right: 20rpx;"
        ></default-avatar>
        <view class="host-details">
          <text class="host-name">{{meetingInfo.host_info.user.name}}</text>
          <text class="host-contact">联系方式：{{meetingInfo.host_info.user.phone}}</text>
          <view class="host-stats">
            <view class="host-stat-item">
              <text class="host-stat-value">{{meetingInfo.host_info.created_count}}</text>
              <text class="host-stat-label">创建的会单</text>
            </view>
            <view class="host-stat-item">
              <text class="host-stat-value">{{meetingInfo.host_info.finished_count}}</text>
              <text class="host-stat-label">已完成会单</text>
            </view>
          </view>
        </view>
        <view class="contact-host-btn cursor-pointer" bindtap="contactHost">联系会东</view>
      </view>
    </view>
    <!-- 会员列表 -->
    <view class="section-card">
      <view class="section-header">
        <text class="section-title">会员列表</text>
        <!-- <view class="add-member-btn cursor-pointer" bindtap="showAddMemberModal">
          <image src="/static/images/add-person.png" class="icon-small"></image>
          <text>添加会员</text>
        </view> -->
      </view>
      <scroll-view scroll-x class="members-scroll" enhanced="{{true}}" show-scrollbar="{{false}}">
        <view class="members-container">
          <view wx:for="{{meetingInfo.member_list}}" wx:key="id" class="member-card cursor-pointer" bindtap="showMemberDetail" data-member="{{item}}">
            <default-avatar
              src="{{item.user.avatar}}"
              name="{{item.user.name}}"
              size="medium"
              custom-class="member-avatar"
            ></default-avatar>
            <text class="member-name">{{item.user.name}}</text>
            <text class="member-join-date">{{item.created_at}}</text>
            <text class="member-status {{item.state !== 'new' ? 'status-inactive' : ''}}">{{item.state_txt}}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    <!-- 会期详情 -->
    <view class="section-card">
      <view class="section-header">
        <text class="section-title">会期详情</text>
        <view class="view-all-btn cursor-pointer" bindtap="viewAllPeriods">
          <text>查看全部</text>
          <image src="/static/images/right.svg" class="icon-small"></image>
        </view>
      </view>
      <view class="periods-table">
        <view class="table-header">
          <text class="table-cell period-num">期数</text>
          <text class="table-cell period-date">日期</text>
          <text class="table-cell period-amount">金额</text>
          <text class="table-cell period-member">会员</text>
          <text class="table-cell period-status">状态</text>
        </view>
        <view wx:for="{{meetingInfo.period_list}}" wx:key="number" class="table-row cursor-pointer" bindtap="showPeriodDetail" data-period="{{item}}">
          <text class="table-cell period-num">第{{item.number}}期</text>
          <text class="table-cell period-date">{{item.s_date}}</text>
          <text class="table-cell period-amount">{{item.amount}}</text>
          <text class="table-cell period-member">{{item.user}}</text>
          <text class="table-cell period-status">
            <text class="status-pill {{item.state === 'finish' ? 'status-completed' : item.state === 'new' ? 'status-ongoing' : 'status-pending'}}">{{item.state_txt}}</text>
          </text>
        </view>
      </view>
    </view>
    <!-- 收付款记录 -->
    <view class="section-card">
      <view class="section-header">
        <text class="section-title">收付款记录</text>
      </view>
      <view class="transaction-timeline">
        <view wx:for="{{record}}" wx:key="index" class="transaction-item">
          <view class="timeline-dot {{item.type === 'income' ? 'dot-income' : 'dot-expense'}}"></view>
          <view class="transaction-content">
            <view class="transaction-header">
              <text class="transaction-date">{{item.date}}</text>
              <text class="transaction-status">已完成</text>
            </view>
            <view class="transaction-details">
              <text class="transaction-type">{{item.type_txt}}</text>
              <text class="transaction-amount {{item.type === 'income' ? 'amount-income' : 'amount-expense'}}">{{item.type === 'income' ? '+' : '-'}}¥{{item.formatted_amount}}</text>
            </view>
            <text class="transaction-operator">{{item.extra}}</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 资金统计 -->
    <view class="section-card">
      <view class="section-header">
        <text class="section-title">资金统计</text>
      </view>
      <view class="stats-summary">
        <view class="stats-item">
          <text class="stats-label">已收款</text>
          <text class="stats-value income-color">¥30,000</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">已付款</text>
          <text class="stats-value expense-color">¥30,000</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">待收款</text>
          <text class="stats-value pending-color">¥70,000</text>
        </view>
      </view>
      <view class="stats-chart">
        <canvas
          canvas-id="statsChart"
          id="statsChart"
          class="chart-canvas"
          type="2d"
          bindtouchstart="onChartTouchStart"
        ></canvas>
        <view class="chart-legend">
          <view class="legend-item">
            <view class="legend-dot income-dot"></view>
            <text class="legend-text">已收款</text>
          </view>
          <view class="legend-item">
            <view class="legend-dot expense-dot"></view>
            <text class="legend-text">已付款</text>
          </view>
          <view class="legend-item">
            <view class="legend-dot pending-dot"></view>
            <text class="legend-text">待收款</text>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
  </view>
  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <view class="action-button record-payment-btn cursor-pointer" bindtap="showRecordPaymentModal">
      <image src="/static/images/more.svg" class="icon-small"></image>
      <text>更多操作</text>
    </view>
    <view class="action-button edit-btn cursor-pointer" bindtap="editMeeting">
      <image src="/static/images/edit.svg" class="icon-small"></image>
      <text>编辑会单</text>
    </view>
    <button open-type="share" class="action-button add-member-btn cursor-pointer invite-share-btn" bindtap="setInviteShare">
      <image src="/static/images/customer-add.svg" class="icon-small"></image>
      <text>邀请会员</text>
    </button>
  </view>
  <!-- 更多操作弹窗 -->
  <view class="popup-mask" wx:if="{{showMoreActions}}" bindtap="closeMoreActions"></view>
  <view class="popup-content" wx:if="{{showMoreActions}}">
    <view class="popup-title">更多操作</view>
    <view class="popup-actions">
      <view class="popup-action-item cursor-pointer" bindtap="shareMeeting">
        <image src="/static/images/share.png" class="icon"></image>
        <text>分享会单</text>
      </view>
      <view class="popup-action-item cursor-pointer" bindtap="exportData">
        <image src="/static/images/download.png" class="icon"></image>
        <text>导出数据</text>
      </view>
      <view class="popup-action-item cursor-pointer" bindtap="archiveMeeting">
        <image src="/static/images/archive.png" class="icon"></image>
        <text>归档会单</text>
      </view>
      <view class="popup-action-item cursor-pointer delete-action" bindtap="deleteMeeting">
        <image src="/static/images/trash.png" class="icon"></image>
        <text>删除会单</text>
      </view>
    </view>
    <button class="popup-cancel cursor-pointer" bindtap="closeMoreActions">
      取消
    </button>
  </view>
  <!-- 添加会员弹窗 -->
  <view class="popup-mask" wx:if="{{showAddMemberPopup}}" bindtap="closeAddMemberModal"></view>
  <view class="form-popup" wx:if="{{showAddMemberPopup}}">
    <view class="form-popup-header">
      <text class="form-popup-title">添加会员</text>
      <image src="/static/images/close.png" class="icon-small cursor-pointer" bindtap="closeAddMemberModal"></image>
    </view>
    <view class="form-popup-content">
      <view class="form-item">
        <text class="form-label">会员姓名</text>
        <input type="text" class="form-input" placeholder="请输入会员姓名" model:value="{{newMember.name}}" />
      </view>
      <view class="form-item">
        <text class="form-label">联系方式</text>
        <input type="text" class="form-input" placeholder="请输入联系方式" model:value="{{newMember.contact}}" />
      </view>
      <view class="form-item">
        <text class="form-label">会员身份</text>
        <view class="radio-group">
          <view class="radio-item cursor-pointer {{newMember.role === '普通会员' ? 'radio-active' : ''}}" bindtap="setMemberRole" data-role="普通会员">
            <view class="radio-dot"></view>
            <text>普通会员</text>
          </view>
          <view class="radio-item cursor-pointer {{newMember.role === '副会东' ? 'radio-active' : ''}}" bindtap="setMemberRole" data-role="副会东">
            <view class="radio-dot"></view>
            <text>副会东</text>
          </view>
        </view>
      </view>
    </view>
    <view class="form-popup-footer">
      <button class="form-btn cancel-btn cursor-pointer" bindtap="closeAddMemberModal">
        取消
      </button>
      <button class="form-btn confirm-btn cursor-pointer" bindtap="confirmAddMember">
        确认
      </button>
    </view>
  </view>
  <!-- 记录收付款弹窗 -->
  <view class="popup-mask" wx:if="{{showRecordPaymentPopup}}" bindtap="closeRecordPaymentModal"></view>
  <view class="form-popup" wx:if="{{showRecordPaymentPopup}}">
    <view class="form-popup-header">
      <text class="form-popup-title">记录收付款</text>
      <image src="/static/images/close.png" class="icon-small cursor-pointer" bindtap="closeRecordPaymentModal"></image>
    </view>
    <view class="form-popup-content">
      <view class="form-item">
        <text class="form-label">操作类型</text>
        <view class="radio-group">
          <view class="radio-item cursor-pointer {{newTransaction.type === '收款' ? 'radio-active' : ''}}" bindtap="setTransactionType" data-type="收款">
            <view class="radio-dot"></view>
            <text>收款</text>
          </view>
          <view class="radio-item cursor-pointer {{newTransaction.type === '付款' ? 'radio-active' : ''}}" bindtap="setTransactionType" data-type="付款">
            <view class="radio-dot"></view>
            <text>付款</text>
          </view>
        </view>
      </view>
      <view class="form-item">
        <text class="form-label">金额</text>
        <input type="digit" class="form-input" placeholder="请输入金额" model:value="{{newTransaction.amount}}" />
      </view>
      <view class="form-item">
        <text class="form-label">相关会员</text>
        <picker class="form-picker" range="{{memberNames}}" bindchange="onMemberPickerChange">
          <view class="picker-value">{{newTransaction.member || '请选择会员'}}</view>
        </picker>
      </view>
      <view class="form-item">
        <text class="form-label">备注</text>
        <textarea class="form-textarea" placeholder="请输入备注信息" model:value="{{newTransaction.remark}}" />
      </view>
    </view>
    <view class="form-popup-footer">
      <button class="form-btn cancel-btn cursor-pointer" bindtap="closeRecordPaymentModal">
        取消
      </button>
      <button class="form-btn confirm-btn cursor-pointer" bindtap="confirmRecordPayment">
        确认
      </button>
    </view>
  </view>
  <!-- 自定义数字键盘 -->
  <view class="popup-mask" wx:if="{{showNumericKeyboard}}" bindtap="closeNumericKeyboard"></view>
  <view class="numeric-keyboard {{showNumericKeyboard ? 'visible' : ''}}">
    <view class="keyboard-header">
      <text class="keyboard-title">投标金额</text>
      <view class="current-bid">
        <text class="bid-label">当前投标</text>
        <text class="bid-amount">{{currentBidAmount ? '¥' + currentBidAmount : '未投标'}}</text>
      </view>
      <view class="input-display">
        <!-- <text class="currency-symbol">¥</text> -->
        <text class="input-value">{{inputAmount || '0'}}</text>
      </view>
    </view>
    <view class="keyboard-grid">
      <view class="key-row">
        <view class="key-btn" bindtap="appendDigit" data-digit="1">1</view>
        <view class="key-btn" bindtap="appendDigit" data-digit="2">2</view>
        <view class="key-btn" bindtap="appendDigit" data-digit="3">3</view>
      </view>
      <view class="key-row">
        <view class="key-btn" bindtap="appendDigit" data-digit="4">4</view>
        <view class="key-btn" bindtap="appendDigit" data-digit="5">5</view>
        <view class="key-btn" bindtap="appendDigit" data-digit="6">6</view>
      </view>
      <view class="key-row">
        <view class="key-btn" bindtap="appendDigit" data-digit="7">7</view>
        <view class="key-btn" bindtap="appendDigit" data-digit="8">8</view>
        <view class="key-btn" bindtap="appendDigit" data-digit="9">9</view>
      </view>
      <view class="key-row">
        <view class="key-btn cancel-btn" bindtap="closeNumericKeyboard">取消</view>
        <view class="key-btn" bindtap="appendDigit" data-digit="0">0</view>
        <view class="key-btn delete-btn" bindtap="deleteDigit">删除</view>
      </view>
    </view>
    <view class="keyboard-footer">
      <button class="confirm-btn" bindtap="confirmBid">确认投标</button>
    </view>
  </view>
</view>