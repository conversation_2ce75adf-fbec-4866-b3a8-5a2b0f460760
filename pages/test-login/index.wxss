.page {
  padding: 40rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.clear-btn {
  background-color: #ff4757;
  color: white;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  border: none;
}

.status-card {
  background-color: white;
  padding: 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.status {
  font-size: 32rpx;
  font-weight: 600;
  color: #3b7cf7;
}

.info-card {
  background-color: white;
  padding: 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.value {
  font-size: 28rpx;
  color: #333;
  max-width: 400rpx;
  word-break: break-all;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.test-btn {
  background-color: #3b7cf7;
  color: white;
  font-size: 28rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  border: none;
}

.test-btn.danger {
  background-color: #ff4757;
}

.logs-container {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.log-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  padding: 8rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  font-family: monospace;
}

.log-item:last-child {
  border-bottom: none;
}

.empty-logs {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx 0;
}

/* 头像测试样式 */
.avatar-test-container {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
  padding: 20rpx;
}

.avatar-test-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.test-label {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

.size-info {
  font-size: 20rpx;
  color: #999;
  text-align: center;
  margin-top: 5rpx;
}
