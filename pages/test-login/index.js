// pages/test-login/index.js
const authManager = require('../../utils/auth.js')

Page({
  data: {
    loginStatus: '未登录',
    userInfo: null,
    logs: []
  },

  onLoad() {
    this.addLog('页面加载完成');
    this.checkLoginStatus();
  },

  // 添加日志
  addLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logs = [...this.data.logs, `[${timestamp}] ${message}`];
    this.setData({ logs });
    console.log(message);
  },

  // 检查登录状态
  async checkLoginStatus() {
    try {
      this.addLog('开始检查登录状态...');

      if (authManager.isLoggedIn()) {
        this.setData({ loginStatus: '已登录（本地）' });
        this.addLog('本地存储显示已登录');

        const userInfo = await authManager.getUserInfo();
        this.setData({ userInfo });
        this.addLog(`获取用户信息成功: ${JSON.stringify(userInfo)}`);
      } else {
        this.setData({ loginStatus: '未登录' });
        this.addLog('本地存储显示未登录');
      }
    } catch (error) {
      this.addLog(`检查登录状态失败: ${error.message}`);
    }
  },

  // 手动登录
  async manualLogin() {
    try {
      this.addLog('开始手动登录...');
      this.setData({ loginStatus: '登录中...' });

      const app = getApp();
      const userInfo = await app.ensureLogin();
      this.setData({
        loginStatus: '登录成功',
        userInfo
      });
      this.addLog(`手动登录成功: ${JSON.stringify(userInfo)}`);
    } catch (error) {
      this.setData({ loginStatus: '登录失败' });
      this.addLog(`手动登录失败: ${error.message}`);
    }
  },

  // 检查用户信息完整性
  async checkUserInfoComplete() {
    try {
      this.addLog('检查用户信息完整性...');
      const isComplete = await authManager.checkUserInfoComplete();
      this.addLog(`用户信息完整性: ${isComplete ? '完整' : '不完整'}`);

      if (!isComplete) {
        wx.navigateTo({
          url: '/pages/complete-profile/index?scene=test'
        });
      }
    } catch (error) {
      this.addLog(`检查用户信息完整性失败: ${error.message}`);
    }
  },

  // 更新用户信息
  async updateUserInfo() {
    try {
      this.addLog('更新用户信息...');
      const newInfo = {
        name: '测试用户' + Date.now(),
        phone: '13800138000'
      };

      const updatedInfo = await authManager.updateUserInfo(newInfo);
      this.setData({ userInfo: updatedInfo });
      this.addLog(`更新用户信息成功: ${JSON.stringify(updatedInfo)}`);
    } catch (error) {
      this.addLog(`更新用户信息失败: ${error.message}`);
    }
  },

  // 退出登录
  logout() {
    this.addLog('退出登录...');
    authManager.logout();
    this.setData({
      loginStatus: '已退出',
      userInfo: null
    });
    this.addLog('退出登录完成');
  },

  // 清空日志
  clearLogs() {
    this.setData({ logs: [] });
  },

  // 获取用户ID
  async getUserId() {
    try {
      const userId = await authManager.getUserId();
      this.addLog(`用户ID: ${userId}`);
    } catch (error) {
      this.addLog(`获取用户ID失败: ${error.message}`);
    }
  }
})
