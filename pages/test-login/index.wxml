<view class="page">
  <view class="header">
    <text class="title">登录测试页面</text>
  </view>

  <!-- 登录状态 -->
  <view class="section">
    <view class="section-title">登录状态</view>
    <view class="status-card">
      <text class="status">{{loginStatus}}</text>
    </view>
  </view>

  <!-- 用户信息 -->
  <view class="section" wx:if="{{userInfo}}">
    <view class="section-title">用户信息</view>
    <view class="info-card">
      <view class="info-item">
        <text class="label">用户ID:</text>
        <text class="value">{{userInfo.userId || '未设置'}}</text>
      </view>
      <view class="info-item">
        <text class="label">姓名:</text>
        <text class="value">{{userInfo.name || '未设置'}}</text>
      </view>
      <view class="info-item">
        <text class="label">手机号:</text>
        <text class="value">{{userInfo.phone || '未设置'}}</text>
      </view>
      <view class="info-item">
        <text class="label">OpenID:</text>
        <text class="value">{{userInfo.openid || '未设置'}}</text>
      </view>
    </view>
  </view>

  <!-- 头像测试 -->
  <view class="section">
    <view class="section-title">缺省头像测试</view>
    <view class="avatar-test-container">
      <view class="avatar-test-item">
        <text class="test-label">有头像URL (medium)</text>
        <default-avatar
          src="https://via.placeholder.com/120x120/3B7CF7/FFFFFF?text=T"
          name="张三"
          size="medium"
        ></default-avatar>
        <text class="size-info">应该是120rpx</text>
      </view>
      <view class="avatar-test-item">
        <text class="test-label">缺省头像 - 张三 (medium)</text>
        <default-avatar
          src=""
          name="张三"
          size="medium"
        ></default-avatar>
        <text class="size-info">应该是120rpx</text>
      </view>
      <view class="avatar-test-item">
        <text class="test-label">缺省头像 - 李四 (medium)</text>
        <default-avatar
          src=""
          name="李四"
          size="medium"
        ></default-avatar>
        <text class="size-info">应该是120rpx</text>
      </view>
      <view class="avatar-test-item">
        <text class="test-label">小尺寸 (small)</text>
        <default-avatar
          src=""
          name="小"
          size="small"
        ></default-avatar>
        <text class="size-info">应该是60rpx</text>
      </view>
      <view class="avatar-test-item">
        <text class="test-label">大尺寸 (large)</text>
        <default-avatar
          src=""
          name="大"
          size="large"
        ></default-avatar>
        <text class="size-info">应该是128rpx</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="section">
    <view class="section-title">操作</view>
    <view class="button-group">
      <button class="test-btn" bindtap="checkLoginStatus">检查登录状态</button>
      <button class="test-btn" bindtap="manualLogin">手动登录</button>
      <button class="test-btn" bindtap="getUserId">获取用户ID</button>
      <button class="test-btn" bindtap="checkUserInfoComplete">检查信息完整性</button>
      <button class="test-btn" bindtap="updateUserInfo">更新用户信息</button>
      <button class="test-btn danger" bindtap="logout">退出登录</button>
    </view>
  </view>

  <!-- 日志 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">操作日志</text>
      <button class="clear-btn" bindtap="clearLogs">清空</button>
    </view>
    <view class="logs-container">
      <view wx:for="{{logs}}" wx:key="index" class="log-item">
        {{item}}
      </view>
      <view wx:if="{{logs.length === 0}}" class="empty-logs">
        暂无日志
      </view>
    </view>
  </view>
</view>
