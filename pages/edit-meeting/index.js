// 编辑会单页面
const { formatMoney, meetingAPI } = require('../../utils/util.js')

Page({
  data: {
    meetingId: '',
    isLoading: true,
    hasChanges: false,
    isSaving: false,

    // 会单信息
    meetingInfo: {},

    // 会期数据
    editablePeriods: [],
    originalPeriods: [],

    // 会员数据
    memberList: [],
    originalMembers: [],

    // 日期选择器
    showDatePicker: false,
    currentEditPeriod: {},
    currentEditIndex: -1,
    minDate: '',

    // 移除会员确认
    showRemoveConfirm: false,
    currentRemoveMember: {},

    // 修改记录
    periodChanges: [],
    memberRemovals: []
  },

  onLoad(options) {
    console.log('编辑会单页面 onLoad 开始');
    const { id } = options;
    if (!id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({ meetingId: id });
    this.loadMeetingData();
    console.log('编辑会单页面 onLoad 完成');
  },

  // 加载会单数据
  async loadMeetingData() {
    try {
      const result = await meetingAPI.getMeetingDetail(this.data.meetingId);

      if (result.success) {
        const meetingData = result.data;

        // 处理会单基本信息
        meetingData.formatted_total_amount = formatMoney(meetingData.total_amount);

        // 处理会期数据
        const editablePeriods = meetingData.periods.map(period => ({
          ...period,
          formatted_date: this.formatDate(period.date)
        }));

        // 处理会员数据
        const memberList = (meetingData.members || []).map(member => ({
          ...member,
          state: member.state || 'active',
          state_txt: member.state_txt || '正常'
        }));

        this.setData({
          meetingInfo: meetingData,
          editablePeriods: editablePeriods,
          originalPeriods: JSON.parse(JSON.stringify(editablePeriods)),
          memberList: memberList,
          originalMembers: JSON.parse(JSON.stringify(memberList)),
          minDate: new Date().toISOString().split('T')[0],
          isLoading: false
        });

        console.log('会单数据加载完成:', meetingData);
      } else {
        throw new Error(result.message || '获取会单信息失败');
      }
    } catch (error) {
      console.error('加载会单数据失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 格式化日期显示
  formatDate(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 编辑会期日期
  editPeriodDate(e) {
    const index = e.currentTarget.dataset.index;
    const period = this.data.editablePeriods[index];

    // 只能编辑未开始的会期
    if (period.status !== 0) {
      wx.showToast({
        title: '只能修改未开始的会期',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showDatePicker: true,
      currentEditPeriod: { ...period },
      currentEditIndex: index
    });
  },

  // 关闭日期选择器
  closeDatePicker() {
    this.setData({
      showDatePicker: false,
      currentEditPeriod: {},
      currentEditIndex: -1
    });
  },

  // 日期选择变更
  onDateChange(e) {
    const newDate = e.detail.value;
    this.setData({
      'currentEditPeriod.date': newDate,
      'currentEditPeriod.formatted_date': this.formatDate(newDate)
    });
  },

  // 确认日期修改
  confirmDateChange() {
    const { currentEditIndex, currentEditPeriod, editablePeriods, periodChanges } = this.data;

    if (currentEditIndex === -1) return;

    // 更新会期数据
    const updatedPeriods = [...editablePeriods];
    updatedPeriods[currentEditIndex] = { ...currentEditPeriod };

    // 记录修改
    const existingChangeIndex = periodChanges.findIndex(change =>
      change.period_id === currentEditPeriod.id
    );

    const newChange = {
      period_id: currentEditPeriod.id,
      period_num: currentEditPeriod.period_num,
      old_date: this.data.originalPeriods[currentEditIndex].date,
      new_date: currentEditPeriod.date
    };

    let updatedChanges = [...periodChanges];
    if (existingChangeIndex >= 0) {
      updatedChanges[existingChangeIndex] = newChange;
    } else {
      updatedChanges.push(newChange);
    }

    this.setData({
      editablePeriods: updatedPeriods,
      periodChanges: updatedChanges,
      hasChanges: true,
      showDatePicker: false,
      currentEditPeriod: {},
      currentEditIndex: -1
    });

    wx.showToast({
      title: '日期修改成功',
      icon: 'success'
    });
  },

  // 移除会员
  removeMember(e) {
    const memberId = e.currentTarget.dataset.memberId;
    const memberName = e.currentTarget.dataset.memberName;

    this.setData({
      showRemoveConfirm: true,
      currentRemoveMember: {
        id: memberId,
        name: memberName
      }
    });
  },

  // 关闭移除确认弹窗
  closeRemoveConfirm() {
    this.setData({
      showRemoveConfirm: false,
      currentRemoveMember: {}
    });
  },

  // 确认移除会员
  confirmRemoveMember() {
    const { currentRemoveMember, memberList, memberRemovals } = this.data;

    // 更新会员列表状态
    const updatedMembers = memberList.map(member => {
      if (member.id === currentRemoveMember.id) {
        return { ...member, state: 'removed', state_txt: '已移除' };
      }
      return member;
    });

    // 记录移除操作
    const updatedRemovals = [...memberRemovals];
    if (!updatedRemovals.find(removal => removal.member_id === currentRemoveMember.id)) {
      updatedRemovals.push({
        member_id: currentRemoveMember.id,
        member_name: currentRemoveMember.name
      });
    }

    this.setData({
      memberList: updatedMembers,
      memberRemovals: updatedRemovals,
      hasChanges: true,
      showRemoveConfirm: false,
      currentRemoveMember: {}
    });

    wx.showToast({
      title: '会员已移除',
      icon: 'success'
    });
  },

  // 取消编辑
  cancelEdit() {
    if (this.data.hasChanges) {
      wx.showModal({
        title: '确认取消',
        content: '您有未保存的修改，确定要取消吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  // 保存修改
  async saveChanges() {
    if (!this.data.hasChanges) {
      wx.navigateBack();
      return;
    }

    // 防止重复提交
    if (this.data.isSaving) {
      return;
    }

    this.setData({ isSaving: true });

    wx.showLoading({
      title: '保存中...'
    });

    try {
      const { meetingId, periodChanges, memberRemovals } = this.data;

      console.log('保存会期修改:', periodChanges);
      console.log('保存会员移除:', memberRemovals);

      // 构建更新数据
      const updateData = {};
      if (periodChanges.length > 0) {
        updateData.period_changes = periodChanges;
      }
      if (memberRemovals.length > 0) {
        updateData.member_removals = memberRemovals;
      }

      // 调用API保存修改
      if (Object.keys(updateData).length > 0) {
        const result = await meetingAPI.updateMeeting(meetingId, updateData);

        if (!result.success) {
          throw new Error(result.message || '保存失败');
        }

        console.log('保存成功:', result);
      }

      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      console.error('保存失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: error.message || '保存失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ isSaving: false });
    }
  }
});
