<!-- 编辑会单页面 -->
<view class="page-wrapper">
  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主体内容 -->
  <view wx:else class="content-container">
    <!-- 会单基本信息 -->
    <view class="section-card">
      <view class="section-header">
        <text class="section-title">会单信息</text>
      </view>
      <view class="meeting-basic-info">
        <view class="info-item">
          <text class="info-label">会单标题</text>
          <text class="info-value">{{meetingInfo.title}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">总金额</text>
          <text class="info-value">¥{{meetingInfo.formatted_total_amount}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">总期数</text>
          <text class="info-value">{{meetingInfo.period_num}}期</text>
        </view>
        <view class="info-item">
          <text class="info-label">会单状态</text>
          <text class="info-value status-text">{{meetingInfo.state_txt}}</text>
        </view>
      </view>
    </view>

    <!-- 会期管理 -->
    <view class="section-card">
      <view class="section-header">
        <text class="section-title">会期管理</text>
        <text class="section-subtitle">仅可编辑未开始的会期</text>
      </view>
      <view class="periods-list">
        <view wx:if="{{editablePeriods.length === 0}}" class="empty-state">
          <text class="empty-text">暂无会期数据</text>
        </view>
        <block wx:else>
          <view wx:for="{{editablePeriods}}" wx:key="id" class="period-item {{item.status !== 0 ? 'disabled' : ''}}">
            <view class="period-info">
              <text class="period-number">第{{item.period_num}}期</text>
              <text class="period-date">{{item.formatted_date}}</text>
              <text class="period-status {{item.status === 0 ? 'status-pending' : 'status-completed'}}">
                {{item.status === 0 ? '未开始' : '已完成'}}
              </text>
            </view>
            <view wx:if="{{item.status === 0}}" class="period-actions">
              <view class="edit-date-btn cursor-pointer" bindtap="editPeriodDate" data-index="{{index}}">
                <image src="/static/images/edit.svg" class="icon-small"></image>
                <text>修改日期</text>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>

    <!-- 会员管理 -->
    <view class="section-card">
      <view class="section-header">
        <text class="section-title">会员管理</text>
        <text class="section-subtitle">共{{memberList.length}}名会员</text>
      </view>
      <view class="members-list">
        <view wx:if="{{memberList.length === 0}}" class="empty-state">
          <text class="empty-text">暂无会员数据</text>
        </view>
        <block wx:else>
          <view wx:for="{{memberList}}" wx:key="id" class="member-item {{item.is_creator ? 'creator' : ''}}">
            <view class="member-info">
              <default-avatar
                src="{{item.user.avatar}}"
                name="{{item.user.name}}"
                size="medium"
                custom-class="member-avatar"
              />
              <view class="member-details">
                <text class="member-name">{{item.user.name}}</text>
                <text class="member-role">{{item.is_creator ? '会东' : '会员'}}</text>
              </view>
            </view>
            <view class="member-actions">
              <view class="member-status {{item.state === 'active' ? 'status-active' : 'status-inactive'}}">
                {{item.state_txt}}
              </view>
              <button wx:if="{{!item.is_creator && item.state === 'active'}}"
                      class="remove-member-btn cursor-pointer"
                      bindtap="removeMember"
                      data-member-id="{{item.id}}"
                      data-member-name="{{item.user.name}}">
                <text class="trash-icon">🗑️</text>
                <text>移除</text>
              </button>
            </view>
          </view>
        </block>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <button class="action-btn secondary-btn" bindtap="cancelEdit" disabled="{{isSaving}}">取消</button>
    <button class="action-btn primary-btn" bindtap="saveChanges" disabled="{{!hasChanges || isSaving}}">
      {{isSaving ? '保存中...' : '保存修改'}}
    </button>
  </view>

  <!-- 日期选择器弹窗 -->
  <view class="popup-mask" wx:if="{{showDatePicker}}" bindtap="closeDatePicker"></view>
  <view class="popup-content date-picker-popup" wx:if="{{showDatePicker}}">
    <view class="popup-header">
      <text class="popup-title">修改第{{currentEditPeriod.period_num}}期日期</text>
      <view class="popup-close" bindtap="closeDatePicker">
        <text class="close-text">×</text>
      </view>
    </view>
    <view class="popup-body">
      <picker mode="date" 
              value="{{currentEditPeriod.date}}" 
              start="{{minDate}}"
              bindchange="onDateChange">
        <view class="date-picker-display">
          <text class="current-date">当前日期：{{currentEditPeriod.formatted_date}}</text>
          <view class="select-date-btn">
            <text class="calendar-icon">📅</text>
            <text>选择新日期</text>
          </view>
        </view>
      </picker>
    </view>
    <view class="popup-actions">
      <button class="popup-btn secondary-btn" bindtap="closeDatePicker">取消</button>
      <button class="popup-btn primary-btn" bindtap="confirmDateChange">确认修改</button>
    </view>
  </view>

  <!-- 移除会员确认弹窗 -->
  <view class="popup-mask" wx:if="{{showRemoveConfirm}}" bindtap="closeRemoveConfirm"></view>
  <view class="popup-content remove-confirm-popup" wx:if="{{showRemoveConfirm}}">
    <view class="popup-header">
      <text class="popup-title">移除会员</text>
    </view>
    <view class="popup-body">
      <text class="confirm-text">确定要移除会员"{{currentRemoveMember.name}}"吗？</text>
      <text class="warning-text">移除后该会员将无法继续参与此会单</text>
    </view>
    <view class="popup-actions">
      <button class="popup-btn secondary-btn" bindtap="closeRemoveConfirm">取消</button>
      <button class="popup-btn danger-btn" bindtap="confirmRemoveMember">确认移除</button>
    </view>
  </view>
</view>
