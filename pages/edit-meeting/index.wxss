/* 编辑会单页面样式 */
.page-wrapper {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #E5E5E5;
  border-top: 4rpx solid #3B7CF7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 14px;
  color: #999999;
}

/* 内容区域 */
.content-container {
  padding: 20rpx 30rpx;
}

/* 通用卡片样式 */
.section-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.section-subtitle {
  font-size: 12px;
  color: #999999;
}

/* 会单基本信息 */
.meeting-basic-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #666666;
}

.info-value {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.status-text {
  color: #3B7CF7;
}

/* 会期列表 */
.periods-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.period-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  border: 1rpx solid #E9ECEF;
}

.period-item.disabled {
  opacity: 0.6;
  background-color: #F5F5F5;
}

.period-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.period-number {
  font-size: 14px;
  font-weight: 600;
  color: #333333;
}

.period-date {
  font-size: 12px;
  color: #666666;
}

.period-status {
  font-size: 12px;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  text-align: center;
  width: fit-content;
}

.status-pending {
  background-color: #FFF3CD;
  color: #856404;
}

.status-completed {
  background-color: #D4EDDA;
  color: #155724;
}

.period-actions {
  display: flex;
  align-items: center;
}

.edit-date-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background-color: #3B7CF7;
  color: white;
  border-radius: 8rpx;
  font-size: 12px;
  transition: all 0.3s ease;
}

.edit-date-btn:active {
  background-color: #2E63D1;
  transform: scale(0.95);
}

/* 会员列表 */
.members-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  border: 1rpx solid #F0F0F0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.member-item:hover {
  border-color: #E0E0E0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.member-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
  min-width: 0;
}

.member-avatar {
  flex-shrink: 0;
}

.member-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
  min-width: 0;
}

.member-name {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.member-role {
  font-size: 12px;
  color: #666666;
  line-height: 1.3;
}

/* 会东特殊样式 */
.member-item.creator {
  border-color: #3B7CF7;
  background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
}

.member-item.creator .member-role {
  color: #3B7CF7;
  font-weight: 500;
}

.member-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-shrink: 0;
}

.member-status {
  font-size: 11px;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-weight: 500;
  white-space: nowrap;
}

.status-active {
  background-color: #E8F5E9;
  color: #2E7D32;
}

.status-inactive {
  background-color: #FFEBEE;
  color: #C62828;
}

.remove-member-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 12rpx 16rpx;
  background-color: #FF4757;
  color: white;
  border-radius: 8rpx;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
  border: none;
  box-shadow: 0 2px 4px rgba(255, 71, 87, 0.2);
}

.remove-member-btn:active {
  background-color: #FF3742;
  transform: translateY(1rpx);
  box-shadow: 0 1px 2px rgba(255, 71, 87, 0.3);
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background-color: white;
  border-top: 1rpx solid #E9ECEF;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 16px;
  font-weight: 500;
  border: none;
}

.primary-btn {
  background-color: #3B7CF7;
  color: white;
}

.primary-btn:disabled {
  background-color: #CCC;
  color: #999;
}

.secondary-btn {
  background-color: #F8F9FA;
  color: #666666;
  border: 1rpx solid #E9ECEF;
}

/* 弹窗样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.popup-content {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  z-index: 1001;
  max-height: 80vh;
  overflow-y: auto;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #E9ECEF;
}

.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.popup-close {
  padding: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #F5F5F5;
}

.close-text {
  font-size: 24px;
  color: #666666;
  line-height: 1;
}

.popup-body {
  padding: 30rpx;
}

.popup-actions {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #E9ECEF;
}

.popup-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 14px;
  font-weight: 500;
  border: none;
}

.danger-btn {
  background-color: #DC3545;
  color: white;
}

/* 日期选择器 */
.date-picker-display {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  align-items: center;
}

.current-date {
  font-size: 14px;
  color: #666666;
}

.select-date-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 40rpx;
  background-color: #3B7CF7;
  color: white;
  border-radius: 12rpx;
  font-size: 14px;
}

.calendar-icon {
  font-size: 16px;
}

.trash-icon {
  font-size: 12px;
  line-height: 1;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
  color: #999999;
}

.empty-text {
  font-size: 14px;
  color: #999999;
}

/* 移除确认弹窗 */
.confirm-text {
  font-size: 16px;
  color: #333333;
  margin-bottom: 16rpx;
  text-align: center;
}

.warning-text {
  font-size: 14px;
  color: #DC3545;
  text-align: center;
}

/* 通用图标 */
.icon-small {
  width: 32rpx;
  height: 32rpx;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .content-container {
    padding: 16rpx 20rpx;
  }

  .section-card {
    padding: 24rpx;
  }
}

/* 光标指针 */
.cursor-pointer {
  cursor: pointer;
}

.edit-date-btn,
.remove-member-btn,
.select-date-btn,
.popup-close {
  cursor: pointer;
}
