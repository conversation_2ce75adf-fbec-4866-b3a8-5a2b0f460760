.page {
  height: 100vh;
  background-color: #f5f7fa;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
}

/* 顶部标题 */
.main-title {
  color: #333333;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  padding: 16px 0;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e7eb;
}

/* 用户信息横幅 */
.user-banner {
  background: linear-gradient(180deg, #5a92f5 0%, #3b7cf7 100%);
  padding: 40px 24px 32px;
  color: white;
  display: flex;
  align-items: center;
  position: relative;
  min-height: 120px;
}

.user-banner .user-avatar {
  margin-right: 120rpx !important;
  border: 4rpx solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 50% !important;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 20rpx;
  padding-left: 20rpx;
}

.user-name {
  font-size: 18px;
  font-weight: 600;
  display: block;
  line-height: 1.3;
  margin-bottom: 12rpx;
}

.phone-section {
  display: flex;
  align-items: center;
  font-size: 14px;
  opacity: 0.9;
  margin-top: 12rpx;
}

.phone-icon {
  font-size: 14px;
  margin-right: 12rpx;
}

.phone-number {
  font-weight: 400;
}

.edit-btn {
  color: white;
  padding: 16rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.edit-btn:active {
  background-color: rgba(255, 255, 255, 0.2);
}

.edit-icon {
  font-size: 18px;
}

/* 统计数据卡片 */
.stats-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 24px;
  margin: -16px 16px 16px 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  text-align: center;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 0;
  border-radius: 12rpx;
  transition: background-color 0.2s;
}

.stat-item:hover {
  background-color: #f8f9fa;
}

.stat-number {
  font-size: 18px;
  font-weight: 600;
  color: #3b7cf7;
  margin-bottom: 8rpx;
  display: block;
}

.stat-label {
  font-size: 12px;
  color: #666666;
  margin-top: 4rpx;
}

/* 功能菜单列表 */
.menu-list {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 0 16px 16px 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
  min-height: 60px;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f9fa;
}

.menu-left {
  display: flex;
  align-items: center;
}

.menu-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f0f4ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.menu-icon {
  font-size: 18px;
  color: #3b7cf7;
}

.menu-title {
  font-size: 16px;
  color: #333333;
  font-weight: 500;
}

.menu-right {
  display: flex;
  align-items: center;
}

.notification-badge {
  background-color: #ff4757;
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  margin-right: 8px;
  min-width: 20px;
  text-align: center;
}

.arrow-icon {
  font-size: 18px;
  color: #999999;
  opacity: 0.6;
}



/* 退出登录 */
.logout-container {
  padding: 40rpx 32rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}

.logout-button {
  width: 100%;
  height: 88rpx;
  background-color: #ffffff;
  color: #ff5252;
  font-size: 14px;
  font-weight: 500;
  border: none;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(255, 82, 82, 0.1);
  transition: all 0.2s;
}

.logout-button:active {
  background-color: #fff5f5;
  transform: scale(0.98);
}

.logout-button::after {
  border: none;
}