<view class="page">
  <!-- 用户信息横幅 -->
  <view class="user-banner">
    <default-avatar
      src="{{userInfo.avatar}}"
      name="{{userInfo.name}}"
      size="large"
      custom-class="user-avatar"
      clickable="{{true}}"
      bind:avatarTap="changeAvatar"
    ></default-avatar>
    <view class="user-info">
      <text class="user-name">{{userInfo.name}}</text>
      <view class="phone-section">
        <text class="phone-icon">📱</text>
        <text class="phone-number">{{userInfo.phone}}</text>
      </view>
    </view>
    <view class="edit-btn" bindtap="editProfile">
      <text class="edit-icon">✏️</text>
    </view>
  </view>

  <!-- 统计数据卡片 -->
  <view class="stats-card">
    <view class="stat-item" bindtap="viewStatDetail" data-type="created">
      <text class="stat-number">{{accountStats.createdMeetings}}</text>
      <text class="stat-label">创建的会单</text>
    </view>
    <view class="stat-item" bindtap="viewStatDetail" data-type="joined">
      <text class="stat-number">{{accountStats.joinedMeetings}}</text>
      <text class="stat-label">参与的会单</text>
    </view>
    <view class="stat-item" bindtap="viewStatDetail" data-type="completed">
      <text class="stat-number">{{accountStats.completedMeetings}}</text>
      <text class="stat-label">已完成会单</text>
    </view>
  </view>

  <!-- 功能菜单列表 -->
  <view class="menu-list">
    <view class="menu-item" bindtap="goToNotifications">
      <view class="menu-left">
        <view class="menu-icon-wrapper">
          <text class="menu-icon">💬</text>
        </view>
        <text class="menu-title">消息通知</text>
      </view>
      <view class="menu-right">
        <text class="notification-badge" wx:if="{{notificationCount > 0}}">{{notificationCount}}</text>
        <text class="arrow-icon">›</text>
      </view>
    </view>

    <view class="menu-item" bindtap="goToAccountSettings">
      <view class="menu-left">
        <view class="menu-icon-wrapper">
          <text class="menu-icon">⚙️</text>
        </view>
        <text class="menu-title">账户设置</text>
      </view>
      <text class="arrow-icon">›</text>
    </view>

    <view class="menu-item" bindtap="goToHelpCenter">
      <view class="menu-left">
        <view class="menu-icon-wrapper">
          <text class="menu-icon">❓</text>
        </view>
        <text class="menu-title">帮助中心</text>
      </view>
      <text class="arrow-icon">›</text>
    </view>

    <view class="menu-item" bindtap="goToAboutUs">
      <view class="menu-left">
        <view class="menu-icon-wrapper">
          <text class="menu-icon">ℹ️</text>
        </view>
        <text class="menu-title">关于我们</text>
      </view>
      <text class="arrow-icon">›</text>
    </view>
  </view>

  <!-- 退出登录按钮 -->
  <view class="logout-container">
    <button class="logout-button" bindtap="logout">退出登录</button>
  </view>
</view>