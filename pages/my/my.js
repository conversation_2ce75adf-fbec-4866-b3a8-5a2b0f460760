const authManager = require('../../utils/auth.js')

Page({
  data: {
    userInfo: {
      avatar: 'https://readdy.ai/api/search-image?query=icon%2C%20Realistic%20portrait%20of%20a%20young%20Asian%20businessman%2C%20professional%20headshot%2C%20high-detail%203D%20rendering%2C%20prominent%20main%20subject%2C%20clear%20and%20sharp%2C%20subject%20fills%2080%20percent%20of%20frame%2C%20isolated%20on%20white%20background%2C%20centered%20composition%2C%20soft%20lighting%2C%20subtle%20shadows%2C%20professional%20photography%20style&width=120&height=120&seq=1&orientation=squarish',
      name: '用户姓名',
      phone: '138****8888'
    },
    accountStats: {
      createdMeetings: 12,
      joinedMeetings: 8,
      completedMeetings: 5
    },
    notificationCount: 5, // 消息通知数量
    isLoading: true
  },

  onLoad() {
    this.loadUserData();
  },

  onShow() {
    // 每次显示页面时刷新用户数据
    this.loadUserData();
  },

  // 加载用户数据
  async loadUserData() {
    try {
      console.log('我的页面开始加载用户数据');
      this.setData({ isLoading: true });

      const app = getApp();
      console.log('调用 app.ensureLogin()');
      const userInfo = await app.ensureLogin();
      console.log('登录成功，用户信息:', userInfo);

      if (userInfo) {
        // 格式化手机号显示
        const formattedPhone = userInfo.phone ?
          userInfo.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') :
          '未设置';

        this.setData({
          userInfo: {
            avatar: userInfo.avatar || this.data.userInfo.avatar,
            name: userInfo.name || '未设置姓名',
            phone: formattedPhone
          }
        });
      }
    } catch (error) {
      console.error('加载用户数据失败:', error);
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },

  // 更换头像
  changeAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        // 这里应该上传头像到服务器
        this.setData({
          'userInfo.avatar': tempFilePath
        });
        wx.showToast({
          title: '头像更新成功',
          icon: 'success'
        });
      }
    });
  },

  // 编辑个人资料
  editProfile() {
    wx.navigateTo({
      url: '/pages/complete-profile/index?scene=edit'
    });
  },

  // 查看统计详情
  viewStatDetail(e) {
    const type = e.currentTarget.dataset.type;
    let title = '';
    switch (type) {
      case 'created':
        title = '我创建的会单';
        break;
      case 'joined':
        title = '我参与的会单';
        break;
      case 'completed':
        title = '已完成的会单';
        break;
    }
    wx.showToast({
      title: `查看${title}`,
      icon: 'none'
    });
  },

  // 我的会单管理
  goToMyMeetings() {
    wx.navigateTo({
      url: '/pages/index/index'
    });
  },

  // 消息通知
  goToNotifications() {
    wx.navigateTo({
      url: '/pages/notifications/index'
    });
  },

  // 账户设置
  goToAccountSettings() {
    wx.navigateTo({
      url: '/pages/account-settings/index'
    });
  },

  // 帮助中心
  goToHelpCenter() {
    wx.navigateTo({
      url: '/pages/help-center/index'
    });
  },

  // 关于我们
  goToAboutUs() {
    wx.navigateTo({
      url: '/pages/about-us/index'
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      confirmColor: '#ff5252',
      success: (res) => {
        if (res.confirm) {
          // 使用authManager退出登录
          authManager.logout();

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });

          // 重新加载用户数据（会触发重新登录）
          setTimeout(() => {
            this.loadUserData();
          }, 1500);
        }
      }
    });
  }
});