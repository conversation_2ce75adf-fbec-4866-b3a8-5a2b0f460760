<view class="page">
  <view class="fixed-header">
    <!-- 搜索栏 -->
    <view class="search-container">
      <view class="search-box">
        <image src="/static/images/search.svg" class="icon-small"></image>
        <input type="text" placeholder="搜索会单标题或会东名称" model:value="{{searchText}}" bindconfirm="onSearchConfirm" confirm-type="search" />
      </view>
    </view>
    <!-- 分类标签 -->
    <view class="tabs">
      <view wx:for="{{tabs}}" wx:key="*this" class="tab-item cursor-pointer {{activeTabIndex === index ? 'active' : ''}}" bindtap="switchTab" data-index="{{index}}">
        {{item}}
      </view>
      <!-- 新增按钮 -->
      <view class="create-btn-container cursor-pointer" bindtap="navigateToCreate">
        <image src="/static/images/plus.svg" class="create-icon"></image>
      </view>
    </view>
  </view>
  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{isLoggedIn ? '正在加载数据...' : '正在登录中...'}}</text>
  </view>

  <!-- 会单列表 -->
  <scroll-view
    wx:else
    scroll-y
    class="meeting-list"
    refresher-enabled="{{true}}"
    refresher-triggered="{{isRefreshing}}"
    bindrefresherrefresh="onRefresh"
    bindscrolltolower="onLoadMore"
    enhanced="{{true}}"
    show-scrollbar="{{false}}"
    lower-threshold="100">
    <view wx:for="{{meetingList}}" wx:key="id" class="meeting-card cursor-pointer" bindtap="navigateToDetail" data-id="{{item.id}}">
      <view class="card-header">
        <view class="card-title-link">
          <view class="card-title">{{item.title}}</view>
        </view>
        <view class="status-tag {{item.state === 'finish' ? 'completed' : ''}}">
          {{item.state_txt}}
        </view>
      </view>
      <view class="card-content">
        <view class="host-info cursor-pointer">
          <default-avatar
            src="{{item.creator.avatar}}"
            name="{{item.creator.name}}"
            size="small"
            custom-class="host-avatar"
            style="margin-right: 10rpx;"
          ></default-avatar>
          <text class="host-name">{{item.creator.name}}</text>
        </view>
        <view class="meeting-info">
          <view class="info-item">
            <text class="info-label">总金额</text>
            <text class="info-value">¥{{item.total_amount}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">会员数</text>
            <text class="info-value">{{item.members_count}}人</text>
          </view>
          <view class="info-item">
            <text class="info-label">总期数</text>
            <text class="info-value">{{item.period_num}}期</text>
          </view>
        </view>
      </view>
      <view class="card-footer">
        <view class="progress-container">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.periods_count / item.period_num * 100}}%"></view>
          </view>
          <text class="progress-text">{{item.periods_count}} / {{item.period_num}}期</text>
        </view>
        <text class="create-date">创建于 {{item.created_at}}</text>
      </view>
    </view>
    <!-- 空状态：只有在登录完成、数据加载完成且确实没有数据时才显示 -->
    <view wx:if="{{!isLoading && isLoggedIn && meetingList.length === 0}}" class="empty-state">
      <image src="/static/images/empty.svg" class="empty-image"></image>
      <text class="empty-text">暂无会单数据</text>
      <button class="create-btn cursor-pointer" bind:tap="navigateToCreate">
        创建我的第一个会单
      </button>
    </view>
  </scroll-view>
</view>