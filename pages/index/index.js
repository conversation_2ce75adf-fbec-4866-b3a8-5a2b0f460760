const tabs = ["全部", "我创建的", "我参与的"];
const { meetingAPI, formatMoney } = require('../../utils/util.js');

Page({
  data: {
    tabs,
    meetingList: [],
    activeTabIndex: 0,
    searchText: '',
    isRefreshing: false,
    isLoadingMore: false,
    hasMoreData: true,
    currentPage: 1,
    pageSize: 10,
    isLoggedIn: false,
    isLoading: true // 初始状态为加载中，避免闪烁
  },

  onLoad() {
    console.log('首页 onLoad 开始');
    this.loginAndLoadData();
    console.log('首页 onLoad 完成');
  },

  // 登录并加载数据
  async loginAndLoadData() {
    console.log('首页开始登录并加载数据');
    this.setData({
      isLoading: true,
      isLoggedIn: false,
      meetingList: [],
    });

    try {
      const app = getApp();
      console.log('调用 app.ensureLogin()');
      const userInfo = await app.ensureLogin();
      console.log('登录成功，用户信息:', userInfo);

      this.setData({ isLoggedIn: true });
      await this.getMettingList();
      this.setData({ isLoading: false });
    } catch (error) {
      console.error('登录失败:', error);
      this.setData({ isLoading: false });
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    }
  },

  async getMettingList(isRefresh = false) {
    if (!this.data.isLoggedIn) {
      console.log('用户未登录，跳过数据加载');
      return;
    }

    if (isRefresh) {
      this.setData({
        currentPage: 1
      });
    }

    try {
      // const app = getApp();
      // const userId = await app.getUserId();

      // if (!userId) {
      //   throw new Error('无法获取用户ID');
      // }

      // 调用真实API获取会单列表
      const params = {
        page: this.data.currentPage,
        pageSize: this.data.pageSize,
        tabIndex: this.data.activeTabIndex,
        searchText: this.data.searchText
      };

      // 这里先使用模拟数据，实际项目中替换为真实API调用
      const result = await meetingAPI.getMeetingList(params);
      console.log('会单列表加载完成:', result);
      if (result.success) {
        // 格式化金额显示
        const formattedData = result.data.map(item => ({
          ...item,
          total_amount: formatMoney(item.total_amount)
        }));

        this.setData({
          meetingList: isRefresh ? formattedData : [...this.data.meetingList, ...formattedData],
          isRefreshing: false,
          isLoadingMore: false,
          hasMoreData: result.data.total > this.data.meetingList.length
        });
      }

      // 模拟API调用
      // const temp = [{
      //   id: "1",
      //   title: "家庭装修互助会",
      //   link: "https://readdy.ai/home/<USER>/b24dd673-cd2e-4083-a9d2-d75d4d83484f",
      //   status: "进行中",
      //   hostAvatar: "https://readdy.ai/api/search-image?query=icon%2C%20Realistic%20portrait%20of%20a%20young%20Asian%20businessman%2C%20professional%20headshot%2C%20high-detail%203D%20rendering%2C%20prominent%20main%20subject%2C%20clear%20and%20sharp%2C%20subject%20fills%2080%20percent%20of%20frame%2C%20isolated%20on%20white%20background%2C%20centered%20composition%2C%20soft%20lighting%2C%20subtle%20shadows%2C%20professional%20photography%20style&width=120&height=120&seq=1&orientation=squarish",
      //   hostName: "张先生",
      //   totalAmount: 100000,
      //   memberCount: 10,
      //   totalPeriods: 10,
      //   currentPeriod: 3,
      //   createDate: "2025-03-15",
      //   contact: "***********",
      //   createdCount: 5,
      //   joinedCount: 3,
      //   completedCount: 2,
      // },
      // {
      //   id: "2",
      //   title: "子女教育金互助会",
      //   status: "进行中",
      //   hostAvatar: "https://readdy.ai/api/search-image?query=icon%2C%20Realistic%20portrait%20of%20a%20middle-aged%20Asian%20woman%2C%20professional%20headshot%2C%20high-detail%203D%20rendering%2C%20prominent%20main%20subject%2C%20clear%20and%20sharp%2C%20subject%20fills%2080%20percent%20of%20frame%2C%20isolated%20on%20white%20background%2C%20centered%20composition%2C%20soft%20lighting%2C%20subtle%20shadows%2C%20professional%20photography%20style&width=120&height=120&seq=2&orientation=squarish",
      //   hostName: "李女士",
      //   totalAmount: 200000,
      //   memberCount: 8,
      //   totalPeriods: 8,
      //   currentPeriod: 5,
      //   createDate: "2025-02-10",
      //   contact: "13987654321",
      //   createdCount: 3,
      //   joinedCount: 7,
      //   completedCount: 1,
      // },
      // {
      //   id: "3",
      //   title: "创业资金互助会",
      //   status: "已完成",
      //   hostAvatar: "https://readdy.ai/api/search-image?query=icon%2C%20Realistic%20portrait%20of%20a%20young%20Asian%20man%20with%20glasses%2C%20professional%20headshot%2C%20high-detail%203D%20rendering%2C%20prominent%20main%20subject%2C%20clear%20and%20sharp%2C%20subject%20fills%2080%20percent%20of%20frame%2C%20isolated%20on%20white%20background%2C%20centered%20composition%2C%20soft%20lighting%2C%20subtle%20shadows%2C%20professional%20photography%20style&width=120&height=120&seq=3&orientation=squarish",
      //   hostName: "王先生",
      //   totalAmount: 500000,
      //   memberCount: 12,
      //   totalPeriods: 12,
      //   currentPeriod: 12,
      //   createDate: "2024-08-20",
      //   contact: "13765432198",
      //   createdCount: 8,
      //   joinedCount: 2,
      //   completedCount: 4,
      // },
      // {
      //   id: "4",
      //   title: "婚礼筹备互助会",
      //   status: "进行中",
      //   hostAvatar: "https://readdy.ai/api/search-image?query=icon%2C%20Realistic%20portrait%20of%20a%20young%20Asian%20woman%20with%20long%20hair%2C%20professional%20headshot%2C%20high-detail%203D%20rendering%2C%20prominent%20main%20subject%2C%20clear%20and%20sharp%2C%20subject%20fills%2080%20percent%20of%20frame%2C%20isolated%20on%20white%20background%2C%20centered%20composition%2C%20soft%20lighting%2C%20subtle%20shadows%2C%20professional%20photography%20style&width=120&height=120&seq=4&orientation=squarish",
      //   hostName: "赵女士",
      //   totalAmount: 80000,
      //   memberCount: 8,
      //   totalPeriods: 8,
      //   currentPeriod: 2,
      //   createDate: "2025-04-05",
      //   contact: "13698765432",
      //   createdCount: 2,
      //   joinedCount: 5,
      //   completedCount: 0,
      // },
      // {
      //   id: "5",
      //   title: "购车互助会",
      //   status: "进行中",
      //   hostAvatar: "https://readdy.ai/api/search-image?query=icon%2C%20Realistic%20portrait%20of%20a%20middle-aged%20Asian%20man%2C%20professional%20headshot%2C%20high-detail%203D%20rendering%2C%20prominent%20main%20subject%2C%20clear%20and%20sharp%2C%20subject%20fills%2080%20percent%20of%20frame%2C%20isolated%20on%20white%20background%2C%20centered%20composition%2C%20soft%20lighting%2C%20subtle%20shadows%2C%20professional%20photography%20style&width=120&height=120&seq=5&orientation=squarish",
      //   hostName: "陈先生",
      //   totalAmount: 150000,
      //   memberCount: 10,
      //   totalPeriods: 10,
      //   currentPeriod: 6,
      //   createDate: "2024-11-12",
      //   contact: "13567891234",
      //   createdCount: 4,
      //   joinedCount: 6,
      //   completedCount: 3,
      // },
      // ];
      // 模拟分页逻辑
      // const { currentPage, pageSize } = this.data;
      // const start = (currentPage - 1) * pageSize;
      // const end = currentPage * pageSize;
      // const pageData = temp.slice(start, end);



      // this.filterMeetingList();
    } catch (error) {
      console.error('获取会单列表失败:', error);
      this.setData({
        isRefreshing: false,
        isLoadingMore: false
      });

      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  async onRefresh() {
    console.log('下拉刷新')
    if (this.data.isRefreshing) return;

    this.setData({ isRefreshing: true });
    await this.getMettingList(true);
  },

  async onLoadMore() {
    console.log('上提加载')
    if (this.data.isLoadingMore || !this.data.hasMoreData) return;

    this.setData({
      isLoadingMore: true,
      currentPage: this.data.currentPage + 1
    });

    await this.getMettingList();
  },



  switchTab(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({ activeTabIndex: index }, () => {
      this.getMettingList(true);
    });
  },

  // 搜索确认事件（键盘确定按钮）
  onSearchConfirm(e) {
    console.log('搜索确认:', e.detail.value);
    this.setData({
      searchText: e.detail.value,
      currentPage: 1
    }, () => {
      this.getMettingList(true);
    });
  },

  navigateToDetail(e) {
    const id = e.currentTarget.dataset.id;
    console.log(id)
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`,
    });
  },

  async navigateToCreate() {
    // 检查用户信息是否完整
    const app = getApp();
    const canProceed = await app.checkAndCompleteUserInfo('create-meeting');

    if (canProceed) {
      wx.navigateTo({
        url: '/pages/create-meeting/index'
      });
    }
  }
})