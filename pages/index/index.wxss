.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  position: relative;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  z-index: 100;
}

.icon-small {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}
/* 搜索栏 */
.search-container {
  padding: 16rpx 30rpx;
  background-color: #ffffff;
  height: 50px;
}
.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 30rpx;
  padding: 12rpx 20rpx;
}
.search-box input {
  flex: 1;
  height: 36rpx;
  margin-left: 10rpx;
  font-size: 14px;
  color: #333;
}
/* 分类标签 */
.tabs {
  display: flex;
  background-color: #ffffff;
  padding: 0 30rpx;
  border-bottom: 1px solid #eeeeee;
  height: 50px;
  align-items: center;
}
.tab-item {
  padding: 0 20rpx;
  height: 50px;
  line-height: 50px;
  font-size: 14px;
  color: #666;
  position: relative;
  margin-right: 30rpx;
}
.tab-item.active {
  color: #3b7cf7;
  font-weight: 500;
}
.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #3b7cf7;
  border-radius: 3px 3px 0 0;
}

/* 新增按钮 */
.create-btn-container {
  margin-left: auto;
  margin-right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
}

.create-icon {
  width: 40rpx;
  height: 40rpx;
}
/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 100px);
  margin-top: 100px;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3b7cf7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 会单列表 */
.meeting-list {
  flex: 1;
  padding: 20rpx 30rpx;
  margin-top: 100px;
  height: calc(100vh - 120px);
  box-sizing: border-box;
}
.meeting-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.card-title-link {
  text-decoration: none;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.card-title:hover {
  color: #3b7cf7;
}
.status-tag {
  font-size: 12px;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  background-color: #e6f0ff;
  color: #3b7cf7;
}
.status-tag.completed {
  background-color: #e8f5e9;
  color: #4caf50;
}
.card-content {
  margin-bottom: 20rpx;
}
.host-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.host-avatar {
  margin-right: 10rpx;
}
.host-name {
  font-size: 14px;
  color: #666;
}
.meeting-info {
  display: flex;
  justify-content: space-between;
}
.info-item {
  display: flex;
  flex-direction: column;
}
.info-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 6rpx;
}
.info-value {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.progress-container {
  display: flex;
  align-items: center;
  flex: 1;
}
.progress-bar {
  flex: 1;
  height: 6px;
  background-color: #eeeeee;
  border-radius: 3px;
  overflow: hidden;
  margin-right: 10rpx;
}
.progress-fill {
  height: 100%;
  background-color: #3b7cf7;
  border-radius: 3px;
}
.progress-text {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}
.create-date {
  font-size: 12px;
  color: #999;
  text-align: right;
  width: 250rpx;
}
/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-image {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 30rpx;
}
.empty-text {
  font-size: 14px;
  color: #999;
  margin-bottom: 30rpx;
}
.create-btn {
  background-color: #3b7cf7;
  color: #ffffff;
  font-size: 14px;
  padding: 16rpx 40rpx;
  border-radius: 30rpx;
  border: none;
}
/* 底部导航栏 */
.tab-bar {
  height: 60px;
  display: flex;
  background-color: #ffffff;
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.05);
}
.tab-bar .tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60px;
  margin-right: 0;
}
.tab-bar .tab-item text {
  font-size: 12px;
  color: #999;
  margin-top: 4rpx;
}
.tab-bar .tab-item .active-text {
  color: #3b7cf7;
}
/* 会东信息弹窗 */
.host-popup {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}
.host-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #eeeeee;
}
.host-popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.host-popup-content {
  padding: 30rpx;
}
.host-popup-profile {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.host-popup-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 20rpx;
}
.host-popup-info {
  flex: 1;
}
.host-popup-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}
.host-popup-contact {
  font-size: 14px;
  color: #666;
  display: block;
}
.host-popup-stats {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-top: 1px solid #eeeeee;
  border-bottom: 1px solid #eeeeee;
}
.host-popup-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.host-popup-stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #3b7cf7;
  margin-bottom: 6rpx;
  display: block;
}
.host-popup-stat-label {
  font-size: 12px;
  color: #999;
  display: block;
}
.host-popup-actions {
  display: flex;
  padding: 30rpx;
}
.host-popup-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 14px;
  margin: 0 10rpx;
}
.message-btn {
  background-color: #3b7cf7;
  color: #ffffff;
  border: none;
}
.view-all-btn {
  background-color: #ffffff;
  color: #3b7cf7;
  border: 1px solid #3b7cf7;
}
.cursor-pointer {
  cursor: pointer;
}