<view class="page">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view class="content" wx:else>
    <!-- 会单信息卡片 -->
    <view class="meeting-card">
      <!-- 会单标题 -->
      <view class="meeting-title">{{meetingInfo.title || meetingInfo.name}}</view>

      <!-- 会东信息 -->
      <view class="host-info" wx:if="{{meetingInfo.creator}}">
        <view class="host-label">会东</view>
        <view class="host-details">
          <default-avatar
            src="{{meetingInfo.creator.avatar}}"
            name="{{meetingInfo.creator.name}}"
            size="medium"
            custom-class="host-avatar"
          ></default-avatar>
          <text class="host-name">{{meetingInfo.creator.name}}</text>
        </view>
      </view>

      <!-- 会员数信息 -->
      <view class="member-info">
        <text class="member-label">已加入会员</text>
        <text class="member-count">{{meetingInfo.memberCount}}人</text>
      </view>
    </view>

    <!-- 加入按钮 -->
    <button
      class="join-btn {{isJoining ? 'loading' : ''}}"
      bindtap="handleJoinMeeting"
      disabled="{{isJoining}}"
    >
      {{isJoining ? '加入中...' : '加入会单'}}
    </button>

    <!-- 用户信息提示 -->
    <view class="user-info-tip" wx:if="{{!isUserInfoComplete}}">
      <view class="tip-icon">ℹ️</view>
      <view class="tip-content">
        <text class="tip-title">需要完善个人信息</text>
        <text class="tip-desc">加入会单前需要完善您的姓名和手机号</text>
      </view>
    </view>
  </view>

  <!-- 错误提示 -->
  <view class="error-container" wx:if="{{errorMessage}}">
    <view class="error-icon">❌</view>
    <view class="error-message">{{errorMessage}}</view>
    <button class="retry-btn" bindtap="retryLoad">重试</button>
  </view>
</view>
