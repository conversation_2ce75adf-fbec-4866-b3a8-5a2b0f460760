.page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3b7cf7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 主要内容 */
.content {
  padding: 30rpx;
}

/* 会单信息卡片 */
.meeting-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.meeting-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

/* 会东信息 */
.host-info {
  margin-bottom: 30rpx;
}

.host-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 15rpx;
  display: block;
}

.host-details {
  display: flex;
  align-items: center;
}

.host-avatar {
  margin-right: 20rpx;
}

.host-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

/* 会员信息 */
.member-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f5f7fa;
  border-radius: 12rpx;
}

.member-label {
  font-size: 14px;
  color: #666;
}

.member-count {
  font-size: 16px;
  color: #3b7cf7;
  font-weight: 600;
}

/* 加入按钮 */
.join-btn {
  width: 100%;
  height: 88rpx;
  background-color: #3b7cf7;
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.join-btn.loading {
  opacity: 0.7;
}

.join-btn:disabled {
  opacity: 0.7;
}

/* 用户信息提示 */
.user-info-tip {
  background-color: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: flex-start;
}

.tip-icon {
  font-size: 30rpx;
  margin-right: 15rpx;
  margin-top: 2rpx;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 14px;
  font-weight: 600;
  color: #856404;
  display: block;
  margin-bottom: 6rpx;
}

.tip-desc {
  font-size: 12px;
  color: #856404;
  line-height: 1.4;
}



/* 错误提示 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  padding: 30rpx;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-message {
  font-size: 14px;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
  line-height: 1.5;
}

.retry-btn {
  padding: 16rpx 32rpx;
  background-color: #3b7cf7;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 14px;
}
