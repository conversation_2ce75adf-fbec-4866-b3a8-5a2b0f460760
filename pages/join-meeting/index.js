const { meetingAPI, formatMoney } = require('../../utils/util.js');
const authManager = require('../../utils/auth.js');

Page({
  data: {
    meetingId: '',
    meetingInfo: {},
    isLoading: true,
    isJoining: false,
    isLoggedIn: false,
    isUserInfoComplete: false,
    errorMessage: '',
    userInfo: null
  },

  onLoad(options) {
    console.log('加入会单页面 onLoad 开始');
    const { meetingId } = options;

    if (!meetingId) {
      this.setData({
        errorMessage: '会单信息不存在，请重新获取邀请链接',
        isLoading: false
      });
      return;
    }

    this.setData({ meetingId });
    this.initPage();
    console.log('加入会单页面 onLoad 完成');
  },

  // 初始化页面
  async initPage() {
    try {
      this.setData({ isLoading: true, errorMessage: '' });

      // 先尝试登录
      await this.checkLoginStatus();

      // 加载会单信息
      await this.loadMeetingInfo();

      this.setData({ isLoading: false });
    } catch (error) {
      console.error('初始化页面失败:', error);
      this.setData({
        errorMessage: error.message || '加载失败，请重试',
        isLoading: false
      });
    }
  },

  // 检查登录状态
  async checkLoginStatus() {
    try {
      const app = getApp();
      const userInfo = await app.ensureLogin();
      console.log('登录成功，用户信息:', userInfo);

      this.setData({
        isLoggedIn: true,
        userInfo
      });

      // 检查用户信息是否完整
      const isComplete = await authManager.checkUserInfoComplete();
      this.setData({ isUserInfoComplete: isComplete });

    } catch (error) {
      console.error('登录失败:', error);
      this.setData({ isLoggedIn: false });
      throw new Error('登录失败，请重试');
    }
  },

  // 加载会单信息
  async loadMeetingInfo() {
    try {
      console.log('获取会单基本信息:', this.data.meetingId);

      const result = await meetingAPI.getMeetingBasicInfo(this.data.meetingId);

      if (result.success) {
        const meetingInfo = result.data;

        // 格式化金额
        if (meetingInfo.totalAmount) {
          meetingInfo.totalAmount = formatMoney(meetingInfo.totalAmount);
        }

        this.setData({ meetingInfo });
        console.log('会单信息加载完成:', meetingInfo);
      } else {
        throw new Error(result.message || '获取会单信息失败');
      }
    } catch (error) {
      console.error('获取会单信息失败:', error);
      throw new Error('会单信息加载失败，请检查邀请链接是否有效');
    }
  },

  // 处理加入会单
  async handleJoinMeeting() {
    if (this.data.isJoining) {
      return;
    }

    try {
      // 检查登录状态
      if (!this.data.isLoggedIn) {
        await this.checkLoginStatus();
      }

      // 检查用户信息是否完整
      if (!this.data.isUserInfoComplete) {
        console.log('用户信息不完整，跳转到完善信息页面');
        wx.navigateTo({
          url: `/pages/complete-profile/index?scene=join-meeting&meetingId=${this.data.meetingId}`
        });
        return;
      }

      this.setData({ isJoining: true });

      // 调用加入会单接口
      const result = await meetingAPI.joinMeeting(this.data.meetingId, {
        name: this.data.userInfo.name,
        phone: this.data.userInfo.phone
      });

      if (result.success) {
        wx.showToast({
          title: '加入成功',
          icon: 'success',
          duration: 2000
        });

        // 延迟跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 2000);
      } else {
        throw new Error(result.message || '加入会单失败');
      }

    } catch (error) {
      console.error('加入会单失败:', error);
      wx.showToast({
        title: error.message || '加入失败，请重试',
        icon: 'none',
        duration: 2000
      });
    } finally {
      this.setData({ isJoining: false });
    }
  },

  // 返回
  goBack() {
    wx.navigateBack({
      fail: () => {
        // 如果无法返回，跳转到首页
        wx.switchTab({
          url: '/pages/index/index'
        });
      }
    });
  },

  // 重试加载
  retryLoad() {
    this.initPage();
  },

  // 页面显示时检查用户信息状态
  onShow() {
    // 如果是从完善信息页面返回，重新检查用户信息完整性
    if (this.data.isLoggedIn) {
      this.checkUserInfoComplete();
    }
  },

  // 检查用户信息完整性
  async checkUserInfoComplete() {
    try {
      const isComplete = await authManager.checkUserInfoComplete();
      this.setData({ isUserInfoComplete: isComplete });

      // 如果用户信息已完整，更新用户信息
      if (isComplete) {
        const app = getApp();
        const userInfo = await app.getUserInfo();
        this.setData({ userInfo });
      }
    } catch (error) {
      console.error('检查用户信息完整性失败:', error);
    }
  },

  // 获取状态对应的CSS类名
  getStatusClass(status) {
    const statusMap = {
      '进行中': 'status-ongoing',
      '未开始': 'status-pending',
      '已结束': 'status-completed'
    };
    return statusMap[status] || 'status-pending';
  }
});
