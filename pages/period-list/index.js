// pages/period-list/index.js
const { formatMoney } = require('../../utils/util.js');

// 模拟会期数据（按倒序排列，最新的在前面）
const mockPeriodList = [
  {
    id: "5",
    number: 5,
    date: "2025-07-22",
    amount: "10,000",
    status: "未开始",
    statusType: "pending",
    bidDeadline: "2025-07-22 11:30",
    bidCount: 0,
    expanded: false,
    bids: []
  },
  {
    id: "4",
    number: 4,
    date: "2025-06-22",
    amount: "10,000",
    status: "未开始",
    statusType: "pending",
    bidDeadline: "2025-06-22 11:30",
    bidCount: 0,
    expanded: false,
    bids: []
  },
  {
    id: "3",
    number: 3,
    date: "2025-05-22",
    amount: "10,000",
    status: "进行中",
    statusType: "ongoing",
    bidDeadline: "2025-05-22 11:30",
    bidCount: 6,
    expanded: true, // 默认展开最新一期
    bids: [
      {
        id: "1",
        name: "王先生",
        amount: "9,800",
        bidTime: "11:25",
        avatar: "https://readdy.ai/api/search-image?query=icon%252C%2520Realistic%2520portrait%2520of%2520a%2520young%2520Asian%2520businessman%252C%2520professional%2520headshot%252C%2520high-detail%25203D%2520rendering%252C%2520prominent%2520main%2520subject%252C%2520clear%2520and%2520sharp%252C%2520subject%2520fills%252080%2520percent%2520of%2520frame%252C%2520isolated%2520on%2520white%2520background%252C%2520centered%2520composition%252C%2520soft%2520lighting%252C%2520subtle%2520shadows%252C%2520professional%2520photography%2520style&width=120&height=120&seq=3&orientation=squarish"
      },
      {
        id: "2",
        name: "李女士",
        amount: "9,500",
        bidTime: "11:20",
        avatar: ""
      },
      {
        id: "3",
        name: "张先生",
        amount: "9,200",
        bidTime: "11:15",
        avatar: "https://readdy.ai/api/search-image?query=icon%252C%2520Realistic%2520portrait%2520of%2520a%2520young%2520Asian%2520businessman%252C%2520professional%2520headshot%252C%2520high-detail%25203D%2520rendering%252C%2520prominent%2520main%2520subject%252C%2520clear%2520and%2520sharp%252C%2520subject%2520fills%252080%2520percent%2520of%2520frame%252C%2520isolated%2520on%2520white%2520background%252C%2520centered%2520composition%252C%2520soft%2520lighting%252C%2520subtle%2520shadows%252C%2520professional%2520photography%2520style&width=120&height=120&seq=1&orientation=squarish"
      },
      {
        id: "4",
        name: "赵女士",
        amount: "9,000",
        bidTime: "11:10",
        avatar: ""
      },
      {
        id: "5",
        name: "陈先生",
        amount: "8,800",
        bidTime: "11:05",
        avatar: ""
      },
      {
        id: "6",
        name: "刘女士",
        amount: "8,500",
        bidTime: "11:00",
        avatar: ""
      }
    ]
  },
  {
    id: "2",
    number: 2,
    date: "2025-04-22",
    amount: "10,000",
    status: "已完成",
    statusType: "completed",
    bidDeadline: "2025-04-22 11:30",
    bidCount: 8,
    expanded: false,
    bids: [
      {
        id: "1",
        name: "李女士",
        amount: "9,600",
        bidTime: "11:28",
        avatar: ""
      },
      {
        id: "2",
        name: "王先生",
        amount: "9,400",
        bidTime: "11:25",
        avatar: "https://readdy.ai/api/search-image?query=icon%252C%2520Realistic%2520portrait%2520of%2520a%2520young%2520Asian%2520businessman%252C%2520professional%2520headshot%252C%2520high-detail%25203D%2520rendering%252C%2520prominent%2520main%2520subject%252C%2520clear%2520and%2520sharp%252C%2520subject%2520fills%252080%2520percent%2520of%2520frame%252C%2520isolated%2520on%2520white%2520background%252C%2520centered%2520composition%252C%2520soft%2520lighting%252C%2520subtle%2520shadows%252C%2520professional%2520photography%2520style&width=120&height=120&seq=3&orientation=squarish"
      },
      {
        id: "3",
        name: "张先生",
        amount: "9,200",
        bidTime: "11:20",
        avatar: "https://readdy.ai/api/search-image?query=icon%252C%2520Realistic%2520portrait%2520of%2520a%2520young%2520Asian%2520businessman%252C%2520professional%2520headshot%252C%2520high-detail%25203D%2520rendering%252C%2520prominent%2520main%2520subject%252C%2520clear%2520and%2520sharp%252C%2520subject%2520fills%252080%2520percent%2520of%2520frame%252C%2520isolated%2520on%2520white%2520background%252C%2520centered%2520composition%252C%2520soft%2520lighting%252C%2520subtle%2520shadows%252C%2520professional%2520photography%2520style&width=120&height=120&seq=1&orientation=squarish"
      },
      {
        id: "4",
        name: "赵女士",
        amount: "9,000",
        bidTime: "11:15",
        avatar: ""
      },
      {
        id: "5",
        name: "陈先生",
        amount: "8,800",
        bidTime: "11:10",
        avatar: ""
      },
      {
        id: "6",
        name: "刘女士",
        amount: "8,600",
        bidTime: "11:08",
        avatar: ""
      },
      {
        id: "7",
        name: "孙先生",
        amount: "8,400",
        bidTime: "11:05",
        avatar: ""
      },
      {
        id: "8",
        name: "周女士",
        amount: "8,200",
        bidTime: "11:00",
        avatar: ""
      }
    ]
  },
  {
    id: "1",
    number: 1,
    date: "2025-03-22",
    amount: "10,000",
    status: "已完成",
    statusType: "completed",
    bidDeadline: "2025-03-22 11:30",
    bidCount: 5,
    expanded: false,
    bids: [
      {
        id: "1",
        name: "张先生",
        amount: "9,700",
        bidTime: "11:29",
        avatar: "https://readdy.ai/api/search-image?query=icon%252C%2520Realistic%2520portrait%2520of%2520a%2520young%2520Asian%2520businessman%252C%2520professional%2520headshot%252C%2520high-detail%25203D%2520rendering%252C%2520prominent%2520main%2520subject%252C%2520clear%2520and%2520sharp%252C%2520subject%2520fills%252080%2520percent%2520of%2520frame%252C%2520isolated%2520on%2520white%2520background%252C%2520centered%2520composition%252C%2520soft%2520lighting%252C%2520subtle%2520shadows%252C%2520professional%2520photography%2520style&width=120&height=120&seq=1&orientation=squarish"
      },
      {
        id: "2",
        name: "李女士",
        amount: "9,300",
        bidTime: "11:25",
        avatar: ""
      },
      {
        id: "3",
        name: "王先生",
        amount: "9,100",
        bidTime: "11:20",
        avatar: "https://readdy.ai/api/search-image?query=icon%252C%2520Realistic%2520portrait%2520of%2520a%2520young%2520Asian%2520businessman%252C%2520professional%2520headshot%252C%2520high-detail%25203D%2520rendering%252C%2520prominent%2520main%2520subject%252C%2520clear%2520and%2520sharp%252C%2520subject%2520fills%252080%2520percent%2520of%2520frame%252C%2520isolated%2520on%2520white%2520background%252C%2520centered%2520composition%252C%2520soft%2520lighting%252C%2520subtle%2520shadows%252C%2520professional%2520photography%2520style&width=120&height=120&seq=3&orientation=squarish"
      },
      {
        id: "4",
        name: "赵女士",
        amount: "8,900",
        bidTime: "11:15",
        avatar: ""
      },
      {
        id: "5",
        name: "陈先生",
        amount: "8,700",
        bidTime: "11:10",
        avatar: ""
      }
    ]
  }
];

Page({
  data: {
    periodList: [],
    isLoading: true,
    isLoggedIn: false,
    meetingId: null
  },

  onLoad(options) {
    console.log('会期详情页 onLoad 开始');
    const { meetingId } = options;
    this.setData({ meetingId });

    this.loginAndLoadData();
    console.log('会期详情页 onLoad 完成');
  },

  // 登录并加载数据
  async loginAndLoadData() {
    console.log('会期详情页开始登录并加载数据');
    this.setData({ isLoading: true, isLoggedIn: false });

    try {
      const app = getApp();
      console.log('调用 app.ensureLogin()');
      const userInfo = await app.ensureLogin();
      console.log('登录成功，用户信息:', userInfo);

      this.setData({ isLoggedIn: true });
      await this.loadPeriodData();
      this.setData({ isLoading: false });
    } catch (error) {
      console.error('登录失败:', error);
      this.setData({ isLoading: false });
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    }
  },

  // 加载会期数据
  async loadPeriodData() {
    try {
      console.log('加载会期数据');

      // TODO: 调用真实API获取会期详情
      // const result = await periodAPI.getPeriodList(this.data.meetingId);

      // 使用模拟数据
      const periodList = mockPeriodList.map(period => ({
        ...period,
        // 格式化金额
        amount: formatMoney(period.amount.replace(/,/g, '')),
        bids: period.bids.map(bid => ({
          ...bid,
          amount: formatMoney(bid.amount.replace(/,/g, ''))
        }))
      }));

      this.setData({ periodList });
      console.log('会期数据加载完成');
    } catch (error) {
      console.error('加载会期数据失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  // 切换期数展开/收起
  togglePeriod(e) {
    const index = e.currentTarget.dataset.index;
    const periodList = [...this.data.periodList];

    // 触发轻微振动反馈
    wx.vibrateShort({
      type: 'light'
    });

    periodList[index].expanded = !periodList[index].expanded;

    this.setData({ periodList });
  }
});
