<view class="page">
  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{isLoggedIn ? '正在加载...' : '正在登录中...'}}</text>
  </view>

  <view wx:else class="page-wrapper">
    <!-- 主体内容区域 -->
    <scroll-view scroll-y class="content-container" enhanced="{{true}}" show-scrollbar="{{false}}">
      <!-- 会期列表 -->
      <view wx:for="{{periodList}}" wx:key="id" class="period-card">
        <!-- 期数标题栏 -->
        <view class="period-header" bindtap="togglePeriod" data-index="{{index}}">
          <view class="period-title-section">
            <text class="period-number">第{{item.number}}期</text>
            <text class="period-date">{{item.date}}</text>
          </view>
          <view class="period-status-section">
            <view class="status-tag status-{{item.statusType}}">{{item.status}}</view>
            <view class="expand-icon {{item.expanded ? 'expanded' : ''}}">
              <text>›</text>
            </view>
          </view>
        </view>

        <!-- 期数详情内容 -->
        <view class="period-content {{item.expanded ? 'expanded' : ''}}">
          <view class="period-info">
            <view class="info-row">
              <text class="info-label">底标金额</text>
              <text class="info-value">¥{{item.amount}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">投标截止</text>
              <text class="info-value">{{item.bidDeadline}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">参与人数</text>
              <text class="info-value">{{item.bidCount}}人</text>
            </view>
          </view>

          <!-- 投标记录 -->
          <view class="bid-records">
            <view class="records-header">
              <text class="records-title">投标记录</text>
              <text class="records-subtitle">按出价从高到低排序</text>
            </view>
            
            <view wx:for="{{item.bids}}" wx:key="id" wx:for-item="bid" wx:for-index="bidIndex" 
                  class="bid-item {{bidIndex === 0 ? 'winner' : ''}}">
              <view class="bid-member">
                <view class="member-avatar">
                  <image wx:if="{{bid.avatar}}" src="{{bid.avatar}}" class="avatar-image" />
                  <view wx:else class="avatar-placeholder">{{bid.name.charAt(0)}}</view>
                </view>
                <view class="member-info">
                  <text class="member-name">{{bid.name}}</text>
                  <text class="bid-time">{{bid.bidTime}}</text>
                </view>
              </view>
              
              <view class="bid-amount-section">
                <text class="bid-amount">¥{{bid.amount}}</text>
                <view wx:if="{{bidIndex === 0}}" class="winner-badge">
                  <text>中标</text>
                </view>
              </view>
            </view>

            <!-- 无投标记录 -->
            <view wx:if="{{!item.bids || item.bids.length === 0}}" class="no-bids">
              <text>暂无投标记录</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view wx:if="{{!periodList || periodList.length === 0}}" class="empty-state">
        <text class="empty-text">暂无会期数据</text>
      </view>
    </scroll-view>
  </view>
</view>
