.page {
  height: 100vh;
  background-color: #F5F7FA;
  display: flex;
  flex-direction: column;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3B7CF7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 页面包装器 */
.page-wrapper {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 内容区域 */
.content-container {
  flex: 1;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  overflow: auto;
}

/* 会期卡片 */
.period-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 期数标题栏 */
.period-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.period-header:active {
  background-color: #f8f9fa;
}

.period-title-section {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.period-status-section {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.period-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.period-date {
  font-size: 28rpx;
  color: #666;
}

.status-tag {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

.status-completed {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-ongoing {
  background-color: #e6f0ff;
  color: #3b7cf7;
}

.status-pending {
  background-color: #f5f5f5;
  color: #999999;
}



.expand-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.expand-icon text {
  font-size: 32rpx;
  color: #999;
  font-weight: bold;
}

/* 期数详情内容 */
.period-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.period-content.expanded {
  max-height: 2000rpx;
}

/* 期数信息 */
.period-info {
  padding: 0 30rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 投标记录 */
.bid-records {
  padding: 30rpx;
}

.records-header {
  margin-bottom: 30rpx;
}

.records-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.records-subtitle {
  font-size: 24rpx;
  color: #999;
}

/* 投标项 */
.bid-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.bid-item:last-child {
  border-bottom: none;
}

.bid-item.winner {
  background: linear-gradient(90deg, rgba(59, 124, 247, 0.05) 0%, rgba(59, 124, 247, 0.02) 100%);
  border-radius: 12rpx;
  padding: 24rpx 20rpx;
  margin: 0 -20rpx;
  border: 1rpx solid rgba(59, 124, 247, 0.1);
  position: relative;
}

.bid-item.winner::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: linear-gradient(180deg, #3B7CF7 0%, #5A92F5 100%);
  border-radius: 3rpx 0 0 3rpx;
}

.bid-member {
  display: flex;
  align-items: center;
  flex: 1;
}

.member-avatar {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #3B7CF7;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
}

.member-info {
  display: flex;
  flex-direction: column;
}

.member-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.bid-time {
  font-size: 24rpx;
  color: #999;
}

.bid-amount-section {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.bid-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.bid-item.winner .bid-amount {
  color: #3B7CF7;
}

.winner-badge {
  background: linear-gradient(90deg, #FF6B35 0%, #F7931E 100%);
  color: white;
  font-size: 22rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3);
}

/* 无投标记录 */
.no-bids {
  text-align: center;
  padding: 60rpx 0;
}

.no-bids text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
