.page {
  min-height: 100vh;
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON>l, sans-serif;
}

/* 标签栏 */
.tab-bar {
  background-color: #ffffff;
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16px 0;
  font-size: 16px;
  color: #6b7280;
  font-weight: 500;
  position: relative;
  transition: color 0.3s ease;
}

.tab-item.active {
  color: #3b7cf7;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background-color: #3b7cf7;
  border-radius: 2px;
}

/* 通知列表 */
.notification-list {
  padding: 16px;
}

.notification-item {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  position: relative;
}

.notification-item:active {
  transform: scale(0.98);
  background-color: #f8f9fa;
}

.notification-item.unread {
  border-left: 4px solid #3b7cf7;
}

.notification-item.read {
  opacity: 0.7;
}

/* 通知图标 */
.notification-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #f0f4ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  position: relative;
  flex-shrink: 0;
}

.notification-icon .icon {
  font-size: 24px;
}

.unread-dot {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background-color: #ff4757;
  border-radius: 50%;
  border: 2px solid #ffffff;
}

/* 通知内容 */
.notification-content {
  flex: 1;
  margin-right: 12px;
}

.notification-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-desc {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 8px;
}

.notification-time {
  font-size: 12px;
  color: #9ca3af;
}

/* 箭头 */
.notification-arrow {
  display: flex;
  align-items: center;
}

.arrow-icon {
  font-size: 20px;
  color: #d1d5db;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #9ca3af;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 16px 20px;
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn.primary {
  background-color: #3b7cf7;
  color: #ffffff;
}

.action-btn.primary:active {
  background-color: #2563eb;
}

.action-btn.secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.action-btn.secondary:active {
  background-color: #e5e7eb;
}

.action-btn::after {
  border: none;
}
