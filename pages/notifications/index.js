const app = getApp()

Page({
  data: {
    activeTab: 'all',
    notifications: [
      {
        id: 1,
        type: 'system',
        icon: '🔔',
        title: '系统维护通知',
        description: '系统将于今晚23:00-01:00进行维护升级，期间可能影响部分功能使用',
        time: '2024-01-15 14:30',
        isRead: false
      },
      {
        id: 2,
        type: 'meeting',
        icon: '💰',
        title: '会单投标提醒',
        description: '您参与的"朋友圈互助会"即将开始第3期投标，请及时参与',
        time: '2024-01-15 10:20',
        isRead: false
      },
      {
        id: 3,
        type: 'meeting',
        icon: '✅',
        title: '会单成功创建',
        description: '您创建的"同事互助会"已成功发布，等待成员加入',
        time: '2024-01-14 16:45',
        isRead: true
      },
      {
        id: 4,
        type: 'system',
        icon: '🎉',
        title: '新功能上线',
        description: '统计分析功能已上线，快去查看您的会单数据分析吧！',
        time: '2024-01-14 09:15',
        isRead: true
      },
      {
        id: 5,
        type: 'meeting',
        icon: '⏰',
        title: '缴费提醒',
        description: '您在"家庭互助会"的第2期缴费即将到期，请及时缴费',
        time: '2024-01-13 18:30',
        isRead: false
      }
    ],
    filteredNotifications: [],
    hasUnreadNotifications: false
  },

  onLoad() {
    this.filterNotifications()
    this.checkUnreadNotifications()
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.filterNotifications()
    this.checkUnreadNotifications()
  },

  // 切换标签
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      activeTab: tab
    })
    this.filterNotifications()
  },

  // 过滤通知
  filterNotifications() {
    const { activeTab, notifications } = this.data
    let filtered = notifications
    
    if (activeTab === 'system') {
      filtered = notifications.filter(item => item.type === 'system')
    } else if (activeTab === 'meeting') {
      filtered = notifications.filter(item => item.type === 'meeting')
    }
    
    this.setData({
      filteredNotifications: filtered
    })
  },

  // 检查是否有未读通知
  checkUnreadNotifications() {
    const hasUnread = this.data.notifications.some(item => !item.isRead)
    this.setData({
      hasUnreadNotifications: hasUnread
    })
  },

  // 标记为已读
  markAsRead(e) {
    const id = e.currentTarget.dataset.id
    const notifications = this.data.notifications.map(item => {
      if (item.id === id) {
        return { ...item, isRead: true }
      }
      return item
    })
    
    this.setData({
      notifications
    })
    this.filterNotifications()
    this.checkUnreadNotifications()
    
    // 这里可以调用API更新服务器状态
    console.log('标记通知为已读:', id)
  },

  // 全部标记为已读
  markAllAsRead() {
    const notifications = this.data.notifications.map(item => ({
      ...item,
      isRead: true
    }))
    
    this.setData({
      notifications
    })
    this.filterNotifications()
    this.checkUnreadNotifications()
    
    wx.showToast({
      title: '已全部标记为已读',
      icon: 'success'
    })
  },

  // 清除已读消息
  clearReadNotifications() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有已读消息吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          const notifications = this.data.notifications.filter(item => !item.isRead)
          this.setData({
            notifications
          })
          this.filterNotifications()
          this.checkUnreadNotifications()
          
          wx.showToast({
            title: '已清除已读消息',
            icon: 'success'
          })
        }
      }
    })
  },

  // 获取当前标签名称
  getTabName() {
    const { activeTab } = this.data
    switch (activeTab) {
      case 'system': return '系统'
      case 'meeting': return '会单'
      default: return ''
    }
  }
})
