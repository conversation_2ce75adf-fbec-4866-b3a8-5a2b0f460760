<view class="page">
  <!-- 顶部标签栏 -->
  <view class="tab-bar">
    <view 
      class="tab-item {{activeTab === 'all' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="all"
    >
      全部
    </view>
    <view 
      class="tab-item {{activeTab === 'system' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="system"
    >
      系统通知
    </view>
    <view 
      class="tab-item {{activeTab === 'meeting' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="meeting"
    >
      会单通知
    </view>
  </view>

  <!-- 通知列表 -->
  <view class="notification-list">
    <view 
      class="notification-item {{item.isRead ? 'read' : 'unread'}}"
      wx:for="{{filteredNotifications}}"
      wx:key="id"
      bindtap="markAsRead"
      data-id="{{item.id}}"
    >
      <view class="notification-icon">
        <text class="icon">{{item.icon}}</text>
        <view class="unread-dot" wx:if="{{!item.isRead}}"></view>
      </view>
      
      <view class="notification-content">
        <view class="notification-title">{{item.title}}</view>
        <view class="notification-desc">{{item.description}}</view>
        <view class="notification-time">{{item.time}}</view>
      </view>
      
      <view class="notification-arrow">
        <text class="arrow-icon">›</text>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{filteredNotifications.length === 0}}">
      <view class="empty-icon">📭</view>
      <view class="empty-text">暂无{{getTabName()}}消息</view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{hasUnreadNotifications}}">
    <button class="action-btn secondary" bindtap="markAllAsRead">
      全部标记为已读
    </button>
    <button class="action-btn primary" bindtap="clearReadNotifications">
      清除已读消息
    </button>
  </view>
</view>
