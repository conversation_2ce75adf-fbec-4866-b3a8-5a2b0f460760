.page {
  height: 100vh;
  background-color: #f5f7fa;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3b7cf7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 顶部概览 */
.overview-section {
  margin-bottom: 20rpx;
}

.overview-card {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.overview-title {
  font-size: 18px;
  font-weight: 600;
}

.overview-date {
  font-size: 14px;
  opacity: 0.8;
}

.overview-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.overview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.overview-value {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.overview-label {
  font-size: 12px;
  opacity: 0.8;
}

.overview-prediction {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.prediction-label {
  font-size: 14px;
  opacity: 0.8;
}

.prediction-value {
  font-size: 16px;
  font-weight: 600;
}

/* 通用卡片样式 */
.section-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

/* 收支趋势 */
.period-selector {
  display: flex;
  background-color: #f5f7fa;
  border-radius: 20rpx;
  padding: 4rpx;
}

.period-option {
  padding: 8rpx 16rpx;
  font-size: 12px;
  color: #666666;
  border-radius: 16rpx;
  cursor: pointer;
}

.period-option.active {
  background-color: #3b7cf7;
  color: #ffffff;
}

.chart-container {
  margin-top: 30rpx;
}

.chart-placeholder {
  height: 300rpx;
  position: relative;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 240rpx;
  padding: 0 20rpx;
}

.chart-bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  margin: 0 5rpx;
}

.chart-bar {
  width: 20rpx;
  margin-bottom: 10rpx;
  border-radius: 10rpx 10rpx 0 0;
  min-height: 10rpx;
}

.income-bar {
  background-color: #4caf50;
  margin-right: 5rpx;
}

.expense-bar {
  background-color: #ff5252;
}

.chart-label {
  font-size: 10px;
  color: #999999;
  margin-top: 10rpx;
}

.chart-legend {
  display: flex;
  justify-content: center;
  margin-top: 20rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 20rpx;
}

.legend-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 6rpx;
  margin-right: 8rpx;
}

.income-dot {
  background-color: #4caf50;
}

.expense-dot {
  background-color: #ff5252;
}

.legend-text {
  font-size: 12px;
  color: #666666;
}

/* 会单参与统计 */
.meeting-stats {
  display: flex;
  justify-content: space-between;
}

.meeting-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.created-circle {
  background-color: #e6f0ff;
  color: #3b7cf7;
}

.joined-circle {
  background-color: #e8f5e9;
  color: #4caf50;
}

.completed-circle {
  background-color: #fff3e0;
  color: #ff9800;
}

.ongoing-circle {
  background-color: #fce4ec;
  color: #e91e63;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
}

.stat-label {
  font-size: 12px;
  color: #666666;
  text-align: center;
}

/* 月度收支明细 */
.month-selector {
  display: flex;
  align-items: center;
}

.picker-text {
  font-size: 14px;
  color: #3b7cf7;
  padding: 8rpx 16rpx;
  background-color: #e6f0ff;
  border-radius: 20rpx;
}

.monthly-summary {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.summary-label {
  font-size: 12px;
  color: #666666;
  margin-bottom: 8rpx;
}

.summary-value {
  font-size: 16px;
  font-weight: 600;
}

.transaction-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #eeeeee;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-info {
  flex: 1;
}

.transaction-title {
  font-size: 14px;
  color: #333333;
  margin-bottom: 6rpx;
  display: block;
}

.transaction-date {
  font-size: 12px;
  color: #999999;
  display: block;
}

.transaction-amount {
  font-size: 16px;
  font-weight: 600;
}

/* 年度报告 */
.annual-report {
  padding: 0;
}

.report-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #eeeeee;
}

.report-item:last-child {
  border-bottom: none;
}

.report-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #f5f7fa;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.report-icon .icon {
  width: 40rpx;
  height: 40rpx;
}

.report-content {
  flex: 1;
}

.report-title {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  margin-bottom: 6rpx;
  display: block;
}

.report-desc {
  font-size: 12px;
  color: #666666;
  display: block;
}

/* 通用颜色类 */
.income-color {
  color: #4caf50;
}

.expense-color {
  color: #ff5252;
}