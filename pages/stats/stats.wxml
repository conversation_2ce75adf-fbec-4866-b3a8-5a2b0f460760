<view class="page">
  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{isLoggedIn ? '正在加载统计数据...' : '正在登录中...'}}</text>
  </view>

  <view wx:else>
    <!-- 顶部概览 -->
    <view class="overview-section">
    <view class="overview-card">
      <view class="overview-header">
        <text class="overview-title">我的统计</text>
        <text class="overview-date">{{currentYear}}年</text>
      </view>
      <view class="overview-stats">
        <view class="overview-item">
          <text class="overview-value">{{totalMeetings}}</text>
          <text class="overview-label">参与会单</text>
        </view>
        <view class="overview-item">
          <text class="overview-value income-color">+¥{{totalIncome}}</text>
          <text class="overview-label">全年收入</text>
        </view>
        <view class="overview-item">
          <text class="overview-value expense-color">-¥{{totalExpense}}</text>
          <text class="overview-label">全年支出</text>
        </view>
      </view>
      <view class="overview-prediction">
        <text class="prediction-label">预计剩余支出</text>
        <text class="prediction-value">¥{{predictedExpense}}</text>
      </view>
    </view>
  </view>

  <!-- 收支趋势 -->
  <view class="section-card">
    <view class="section-header">
      <text class="section-title">收支趋势</text>
      <view class="period-selector">
        <view wx:for="{{periodOptions}}" wx:key="*this"
              class="period-option {{selectedPeriod === item ? 'active' : ''}}"
              bindtap="switchPeriod" data-period="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
    <view class="chart-container">
      <view class="chart-placeholder">
        <view class="chart-bars">
          <view wx:for="{{chartData}}" wx:key="month" class="chart-bar-group">
            <view class="chart-bar income-bar" style="height: {{item.incomeHeight}}%"></view>
            <view class="chart-bar expense-bar" style="height: {{item.expenseHeight}}%"></view>
            <text class="chart-label">{{item.month}}</text>
          </view>
        </view>
        <view class="chart-legend">
          <view class="legend-item">
            <view class="legend-dot income-dot"></view>
            <text class="legend-text">收入</text>
          </view>
          <view class="legend-item">
            <view class="legend-dot expense-dot"></view>
            <text class="legend-text">支出</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 会单参与统计 -->
  <view class="section-card">
    <view class="section-header">
      <text class="section-title">会单参与统计</text>
    </view>
    <view class="meeting-stats">
      <view class="meeting-stat-item">
        <view class="stat-circle created-circle">
          <text class="stat-number">{{createdMeetings}}</text>
        </view>
        <text class="stat-label">创建的会单</text>
      </view>
      <view class="meeting-stat-item">
        <view class="stat-circle joined-circle">
          <text class="stat-number">{{joinedMeetings}}</text>
        </view>
        <text class="stat-label">参与的会单</text>
      </view>
      <view class="meeting-stat-item">
        <view class="stat-circle completed-circle">
          <text class="stat-number">{{completedMeetings}}</text>
        </view>
        <text class="stat-label">已完成会单</text>
      </view>
      <view class="meeting-stat-item">
        <view class="stat-circle ongoing-circle">
          <text class="stat-number">{{ongoingMeetings}}</text>
        </view>
        <text class="stat-label">进行中会单</text>
      </view>
    </view>
  </view>

  <!-- 月度收支明细 -->
  <view class="section-card">
    <view class="section-header">
      <text class="section-title">月度收支明细</text>
      <view class="month-selector">
        <picker mode="date" fields="month" value="{{selectedMonth}}" bindchange="onMonthChange">
          <view class="picker-text">{{selectedMonth}}</view>
        </picker>
      </view>
    </view>
    <view class="monthly-summary">
      <view class="summary-item">
        <text class="summary-label">本月收入</text>
        <text class="summary-value income-color">+¥{{monthlyIncome}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">本月支出</text>
        <text class="summary-value expense-color">-¥{{monthlyExpense}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">净收支</text>
        <text class="summary-value {{monthlyNet >= 0 ? 'income-color' : 'expense-color'}}">
          {{monthlyNet >= 0 ? '+' : ''}}¥{{monthlyNet}}
        </text>
      </view>
    </view>
    <view class="transaction-list">
      <view wx:for="{{monthlyTransactions}}" wx:key="id" class="transaction-item">
        <view class="transaction-info">
          <text class="transaction-title">{{item.title}}</text>
          <text class="transaction-date">{{item.date}}</text>
        </view>
        <text class="transaction-amount {{item.type === 'income' ? 'income-color' : 'expense-color'}}">
          {{item.type === 'income' ? '+' : '-'}}¥{{item.amount}}
        </text>
      </view>
    </view>
  </view>

  <!-- 年度报告 -->
  <view class="section-card">
    <view class="section-header">
      <text class="section-title">年度报告</text>
    </view>
    <view class="annual-report">
      <view class="report-item">
        <view class="report-icon">
          <image src="/static/images/charts-bar.png" class="icon"></image>
        </view>
        <view class="report-content">
          <text class="report-title">最活跃月份</text>
          <text class="report-desc">{{mostActiveMonth}}，参与了{{mostActiveCount}}个会单</text>
        </view>
      </view>
      <view class="report-item">
        <view class="report-icon">
          <image src="/static/images/money-rmb.svg" class="icon"></image>
        </view>
        <view class="report-content">
          <text class="report-title">最大单笔收入</text>
          <text class="report-desc">¥{{maxIncome}}，来自{{maxIncomeSource}}</text>
        </view>
      </view>
      <view class="report-item">
        <view class="report-icon">
          <image src="/static/images/customer.png" class="icon"></image>
        </view>
        <view class="report-content">
          <text class="report-title">合作次数最多</text>
          <text class="report-desc">与{{topPartner}}合作了{{topPartnerCount}}次</text>
        </view>
      </view>
    </view>
  </view>
  </view>
</view>