const { formatMoney } = require('../../utils/util.js');

Page({
  data: {
    currentYear: new Date().getFullYear(),
    totalMeetings: 8,
    totalIncome: formatMoney(45000),
    totalExpense: formatMoney(32000),
    predictedExpense: formatMoney(18000),

    // 收支趋势
    periodOptions: ['近6月', '近1年', '全部'],
    selectedPeriod: '近6月',
    chartData: [
      { month: '1月', incomeHeight: 60, expenseHeight: 40 },
      { month: '2月', incomeHeight: 80, expenseHeight: 60 },
      { month: '3月', incomeHeight: 45, expenseHeight: 70 },
      { month: '4月', incomeHeight: 90, expenseHeight: 50 },
      { month: '5月', incomeHeight: 70, expenseHeight: 80 },
      { month: '6月', incomeHeight: 85, expenseHeight: 45 }
    ],

    // 会单参与统计
    createdMeetings: 3,
    joinedMeetings: 5,
    completedMeetings: 2,
    ongoingMeetings: 6,

    // 月度收支明细
    selectedMonth: '2024-12',
    monthlyIncome: formatMoney(8500),
    monthlyExpense: formatMoney(6200),
    monthlyNet: formatMoney(2300),
    monthlyTransactions: [
      {
        id: 1,
        title: '朋友聚会会单 - 第3期中标',
        date: '12-15',
        type: 'income',
        amount: formatMoney(10000)
      },
      {
        id: 2,
        title: '家庭理财会单 - 第2期缴费',
        date: '12-10',
        type: 'expense',
        amount: formatMoney(5000)
      },
      {
        id: 3,
        title: '同事互助会单 - 第1期缴费',
        date: '12-05',
        type: 'expense',
        amount: formatMoney(3000)
      }
    ],

    // 年度报告
    mostActiveMonth: '6月',
    mostActiveCount: 4,
    maxIncome: formatMoney(15000),
    maxIncomeSource: '朋友聚会会单',
    topPartner: '张小明',
    topPartnerCount: 3,

    // 登录状态
    isLoading: true,
    isLoggedIn: false
  },

  onLoad() {
    console.log('统计页 onLoad 开始');
    this.loginAndLoadData();
    console.log('统计页 onLoad 完成');
  },

  // 登录并加载数据
  async loginAndLoadData() {
    console.log('统计页开始登录并加载数据');
    this.setData({
      isLoading: true,
      isLoggedIn: false
    });

    try {
      const app = getApp();
      console.log('调用 app.ensureLogin()');
      const userInfo = await app.ensureLogin();
      console.log('登录成功，用户信息:', userInfo);

      this.setData({ isLoggedIn: true });
      await this.loadStatsData();
      this.setData({ isLoading: false });
    } catch (error) {
      console.error('登录失败:', error);
      this.setData({ isLoading: false });
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    }
  },

  // 加载统计数据
  async loadStatsData() {
    if (!this.data.isLoggedIn) {
      console.log('用户未登录，跳过数据加载');
      return;
    }

    try {
      console.log('加载统计数据');

      // TODO: 调用真实API获取统计数据
      // const app = getApp();
      // const userId = await app.getUserId();
      // const result = await statsAPI.getStatsData(userId);

      // 这里使用模拟数据，实际项目中替换为真实API调用
      console.log('统计数据加载完成');
    } catch (error) {
      console.error('获取统计数据失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  // 切换时间周期
  switchPeriod(e) {
    const period = e.currentTarget.dataset.period;
    this.setData({
      selectedPeriod: period
    });
    this.updateChartData(period);
  },

  // 更新图表数据
  updateChartData(period) {
    let chartData = [];
    if (period === '近6月') {
      chartData = [
        { month: '7月', incomeHeight: 60, expenseHeight: 40 },
        { month: '8月', incomeHeight: 80, expenseHeight: 60 },
        { month: '9月', incomeHeight: 45, expenseHeight: 70 },
        { month: '10月', incomeHeight: 90, expenseHeight: 50 },
        { month: '11月', incomeHeight: 70, expenseHeight: 80 },
        { month: '12月', incomeHeight: 85, expenseHeight: 45 }
      ];
    } else if (period === '近1年') {
      chartData = [
        { month: '1月', incomeHeight: 50, expenseHeight: 30 },
        { month: '2月', incomeHeight: 60, expenseHeight: 45 },
        { month: '3月', incomeHeight: 40, expenseHeight: 60 },
        { month: '4月', incomeHeight: 80, expenseHeight: 40 },
        { month: '5月', incomeHeight: 65, expenseHeight: 70 },
        { month: '6月', incomeHeight: 90, expenseHeight: 55 },
        { month: '7月', incomeHeight: 60, expenseHeight: 40 },
        { month: '8月', incomeHeight: 80, expenseHeight: 60 },
        { month: '9月', incomeHeight: 45, expenseHeight: 70 },
        { month: '10月', incomeHeight: 90, expenseHeight: 50 },
        { month: '11月', incomeHeight: 70, expenseHeight: 80 },
        { month: '12月', incomeHeight: 85, expenseHeight: 45 }
      ];
    }
    this.setData({ chartData });
  },

  // 月份选择
  onMonthChange(e) {
    const selectedMonth = e.detail.value;
    this.setData({
      selectedMonth: selectedMonth
    });
    this.loadMonthlyData(selectedMonth);
  },

  // 加载月度数据
  loadMonthlyData(month) {
    // 这里应该根据选择的月份加载对应的数据
    console.log('加载月度数据:', month);
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadStatsData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  }
});