/**
 * 环境配置文件
 * 用于管理不同环境下的配置项
 */

// 获取当前环境
const getEnvInfo = () => {
  try {
    const accountInfo = wx.getAccountInfoSync();
    return {
      env: accountInfo.miniProgram.envVersion,
      version: accountInfo.miniProgram.version,
      appId: accountInfo.miniProgram.appId
    };
  } catch (error) {
    console.error('获取环境信息失败:', error);
    return { env: 'develop', version: '0.0.0', appId: '' };
  }
};

// 环境配置
const envConfig = {
  develop: {
    apiBaseUrl: 'http://127.0.0.1:8040/api',
    wsBaseUrl: 'ws://127.0.0.1:8040',
    debug: true
  },
  trial: {
    apiBaseUrl: 'https://hqapi.okbyby.com/v2/api',
    wsBaseUrl: 'wss://test-api.example.com',
    debug: true
  },
  release: {
    apiBaseUrl: 'https://hqapi.okbyby.com/v2/api',
    wsBaseUrl: 'wss://api.example.com',
    debug: false
  }
};

// 获取当前环境的配置
const getConfig = () => {
  const { env } = getEnvInfo();
  return envConfig[env] || envConfig.develop;
};

module.exports = {
  getEnvInfo,
  getConfig
};