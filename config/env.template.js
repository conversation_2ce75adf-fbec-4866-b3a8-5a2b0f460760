/**
 * 环境配置模板文件
 * 使用方法：
 * 1. 复制此文件并重命名为 env.js
 * 2. 根据实际环境修改配置值
 * 3. 确保 env.js 已添加到 .gitignore
 */

// 获取当前环境
const getEnvInfo = () => {
  try {
    const accountInfo = wx.getAccountInfoSync();
    return {
      env: accountInfo.miniProgram.envVersion,
      version: accountInfo.miniProgram.version,
      appId: accountInfo.miniProgram.appId
    };
  } catch (error) {
    console.error('获取环境信息失败:', error);
    return { env: 'develop', version: '0.0.0', appId: '' };
  }
};

// 环境配置
const envConfig = {
  develop: {
    apiBaseUrl: 'http://localhost:8040/api', // 开发环境API地址
    wsBaseUrl: 'ws://localhost:8040',
    debug: true
  },
  trial: {
    apiBaseUrl: 'https://test-api.your-domain.com/api', // 测试环境API地址
    wsBaseUrl: 'wss://test-api.your-domain.com',
    debug: true
  },
  release: {
    apiBaseUrl: 'https://api.your-domain.com/api', // 生产环境API地址
    wsBaseUrl: 'wss://api.your-domain.com',
    debug: false
  }
};

// 获取当前环境的配置
const getConfig = () => {
  const { env } = getEnvInfo();
  return envConfig[env] || envConfig.develop;
};

module.exports = {
  getEnvInfo,
  getConfig
};