// 简化版的ECharts，仅包含饼图功能
const echarts = {
  init: function (canvas, width, height, dpr) {
    const chart = new Chart(canvas, width, height, dpr);
    return chart;
  },

  setCanvasCreator: function (creator) {
    this._canvasCreator = creator;
  },

  // 添加缺失的方法
  registerPreprocessor: function (preprocessor) {
    // 简化实现，不做实际处理
  },

  registerProcessor: function (processor) {
    // 简化实现，不做实际处理
  },

  registerAction: function (action) {
    // 简化实现，不做实际处理
  },

  registerCoordinateSystem: function (coordSys) {
    // 简化实现，不做实际处理
  },

  registerLayout: function (layout) {
    // 简化实现，不做实际处理
  },

  registerVisual: function (visual) {
    // 简化实现，不做实际处理
  },

  registerTransform: function (transform) {
    // 简化实现，不做实际处理
  },

  registerLoading: function (loading) {
    // 简化实现，不做实际处理
  },

  registerMap: function (map) {
    // 简化实现，不做实际处理
  },

  getInstanceByDom: function (dom) {
    return null;
  },

  dispose: function (chart) {
    // 简化实现，不做实际处理
  },

  getMap: function (mapName) {
    return null;
  },

  connect: function (group) {
    // 简化实现，不做实际处理
  },

  disconnect: function (group) {
    // 简化实现，不做实际处理
  },

  disConnect: function (group) {
    // 兼容旧版本API
    this.disconnect(group);
  },

  graphic: {},

  number: {},

  format: {},

  matrix: {},

  vector: {},

  color: {},

  util: {},

  helper: {},

  zrender: {},

  env: {
    browser: {},
    node: false,
    wxa: true,
    worker: false
  }
};

class Chart {
  constructor(canvas, width, height, dpr) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.width = width;
    this.height = height;
    this.dpr = dpr || 1;
    this.option = null;
  }

  setOption(option) {
    this.option = option;
    this.render();
  }

  render() {
    if (!this.option) return;

    const ctx = this.ctx;
    const width = this.width;
    const height = this.height;

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    if (this.option.series && this.option.series[0] && this.option.series[0].type === 'pie') {
      this.renderPie(this.option.series[0]);
    }
  }

  renderPie(series) {
    const ctx = this.ctx;
    const data = series.data || [];
    const centerX = this.width / 2;
    const centerY = this.height / 2;
    const radius = Math.min(this.width, this.height) / 3;
    const innerRadius = radius * 0.6; // 环形图内半径

    // 计算总值
    const total = data.reduce((sum, item) => sum + item.value, 0);

    let currentAngle = -Math.PI / 2; // 从顶部开始

    data.forEach((item, index) => {
      const angle = (item.value / total) * 2 * Math.PI;
      const color = this.getColor(index);

      // 绘制扇形
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + angle);
      ctx.arc(centerX, centerY, innerRadius, currentAngle + angle, currentAngle, true);
      ctx.closePath();
      ctx.fillStyle = color;
      ctx.fill();

      currentAngle += angle;
    });

    // 绘制中心文字
    ctx.fillStyle = '#333';
    ctx.font = '24px sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('总金额', centerX, centerY - 15);

    ctx.font = '28px sans-serif';
    ctx.fillText(`¥${this.formatMoney(total)}`, centerX, centerY + 15);
  }

  getColor(index) {
    const colors = ['#4CAF50', '#FF5252', '#FF9800', '#2196F3', '#9C27B0'];
    return colors[index % colors.length];
  }

  formatMoney(amount) {
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  getZr() {
    return {
      handler: {
        dispatch: () => { },
        processGesture: () => { }
      }
    };
  }

  // 添加其他必要的方法
  resize() {
    // 图表大小调整
  }

  clear() {
    // 清空图表
    if (this.ctx) {
      this.ctx.clearRect(0, 0, this.width, this.height);
    }
  }

  dispose() {
    // 销毁图表实例
    this.option = null;
    this.canvas = null;
    this.ctx = null;
  }

  getWidth() {
    return this.width;
  }

  getHeight() {
    return this.height;
  }

  getDom() {
    return this.canvas;
  }

  getOption() {
    return this.option;
  }

  on() {
    // 事件监听
  }

  off() {
    // 取消事件监听
  }

  dispatchAction() {
    // 分发动作
  }

  showLoading() {
    // 显示加载状态
  }

  hideLoading() {
    // 隐藏加载状态
  }

  getDataURL() {
    // 获取图表图片
    return '';
  }

  getConnectedDataURL() {
    // 获取关联图表图片
    return '';
  }

  convertToPixel() {
    // 坐标转换
    return [0, 0];
  }

  convertFromPixel() {
    // 坐标转换
    return [0, 0];
  }

  containPixel() {
    // 像素点检测
    return false;
  }
}

module.exports = echarts;
