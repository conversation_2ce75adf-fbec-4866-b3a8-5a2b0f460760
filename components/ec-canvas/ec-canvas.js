const echarts = require('./echarts-simple.js');

let ctx;

function compareVersion(v1, v2) {
  v1 = v1.split('.')
  v2 = v2.split('.')
  const len = Math.max(v1.length, v2.length)

  while (v1.length < len) {
    v1.push('0')
  }
  while (v2.length < len) {
    v2.push('0')
  }

  for (let i = 0; i < len; i++) {
    const num1 = parseInt(v1[i])
    const num2 = parseInt(v2[i])

    if (num1 > num2) {
      return 1
    } else if (num1 < num2) {
      return -1
    }
  }
  return 0
}

Component({
  properties: {
    canvasId: {
      type: String,
      value: 'ec-canvas'
    },

    ec: {
      type: Object
    },

    forceUseOldCanvas: {
      type: Boolean,
      value: false
    }
  },

  data: {
    isUseNewCanvas: false
  },

  ready: function () {
    // 简化的ready方法
    if (!this.data.ec) {
      console.warn('组件需绑定 ec 变量，例：<ec-canvas id="mychart-dom-bar" '
        + 'canvas-id="mychart-bar" ec="{{ ec }}"></ec-canvas>');
      return;
    }

    if (!this.data.ec.lazyLoad) {
      this.init();
    }
  },

  methods: {
    init: function (callback) {
      // 简化初始化，直接使用新的Canvas API
      this.setData({ isUseNewCanvas: true });
      this.initByNewWay(callback);
    },



    initByNewWay(callback) {
      // 延迟执行，确保DOM已经渲染完成
      setTimeout(() => {
        const query = wx.createSelectorQuery().in(this)
        query
          .select('.ec-canvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            if (!res || !res[0] || !res[0].node) {
              console.error('Canvas节点获取失败，请检查canvas是否正确渲染');
              return;
            }

            const canvasNode = res[0].node
            const canvasDpr = wx.getSystemInfoSync().pixelRatio
            const canvasWidth = res[0].width
            const canvasHeight = res[0].height

            try {
              const ctx = canvasNode.getContext('2d')

              if (!ctx) {
                console.error('无法获取Canvas 2D上下文');
                return;
              }

              const canvas = new WxCanvas(ctx, this.data.canvasId, true, canvasNode)
              echarts.setCanvasCreator(() => {
                return canvas
              })

              if (typeof callback === 'function') {
                this.chart = callback(canvas, canvasWidth, canvasHeight, canvasDpr);
              } else if (this.data.ec && typeof this.data.ec.onInit === 'function') {
                this.chart = this.data.ec.onInit(canvas, canvasWidth, canvasHeight, canvasDpr);
              } else {
                this.triggerEvent('init', {
                  canvas: canvas,
                  width: canvasWidth,
                  height: canvasHeight,
                  canvasDpr: canvasDpr
                });
              }
            } catch (error) {
              console.error('Canvas初始化失败:', error);
            }
          })
      }, 100); // 延迟100ms执行
    },

    canvasToTempFilePath(opt) {
      // 简化版本，只支持新Canvas API
      const query = wx.createSelectorQuery().in(this);
      query
        .select('.ec-canvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          const canvasNode = res[0].node;
          opt.canvas = canvasNode;
          wx.canvasToTempFilePath(opt);
        });
    },

    touchStart(e) {
      if (this.chart && e.touches.length > 0) {
        var touch = e.touches[0];
        var handler = this.chart.getZr().handler;
        handler.dispatch('mousedown', {
          zrX: touch.x,
          zrY: touch.y
        });
        handler.dispatch('mousemove', {
          zrX: touch.x,
          zrY: touch.y
        });
        handler.processGesture(wrapTouch(e), 'start');
      }
    },

    touchMove(e) {
      if (this.chart && e.touches.length > 0) {
        var touch = e.touches[0];
        var handler = this.chart.getZr().handler;
        handler.dispatch('mousemove', {
          zrX: touch.x,
          zrY: touch.y
        });
        handler.processGesture(wrapTouch(e), 'change');
      }
    },

    touchEnd(e) {
      if (this.chart) {
        const touch = e.changedTouches ? e.changedTouches[0] : {};
        var handler = this.chart.getZr().handler;
        handler.dispatch('mouseup', {
          zrX: touch.x,
          zrY: touch.y
        });
        handler.dispatch('click', {
          zrX: touch.x,
          zrY: touch.y
        });
        handler.processGesture(wrapTouch(e), 'end');
      }
    }
  }
});

function wrapTouch(event) {
  for (let i = 0; i < event.touches.length; ++i) {
    const touch = event.touches[i];
    touch.offsetX = touch.x;
    touch.offsetY = touch.y;
  }
  return event;
}

function WxCanvas(ctx, canvasId, isNew, canvasNode) {
  this.ctx = ctx;
  this.canvasId = canvasId;
  this.chart = null;
  this.isNew = isNew;
  this.canvasNode = canvasNode;

  // mock canvas
  this.canvas = this;
}

WxCanvas.prototype.getContext = function (contextType, contextAttributes) {
  if (contextType === '2d') {
    return this.ctx;
  }
};

WxCanvas.prototype.setChart = function (chart) {
  this.chart = chart;
};

WxCanvas.prototype.attachEvent = function () {
  // noop
};

WxCanvas.prototype.detachEvent = function () {
  // noop
};



WxCanvas.prototype.drawImage = function () {
  this.ctx.drawImage.apply(this.ctx, arguments);
};

WxCanvas.prototype.getImageData = function () {
  return this.ctx.getImageData.apply(this.ctx, arguments);
};

module.exports = { WxCanvas };
