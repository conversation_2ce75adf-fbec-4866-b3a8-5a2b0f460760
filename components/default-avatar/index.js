// components/default-avatar/index.js
Component({
  properties: {
    // 头像URL
    src: {
      type: String,
      value: ''
    },
    // 用户名称，用于生成缺省头像
    name: {
      type: String,
      value: ''
    },
    // 头像尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      value: false
    }
  },

  data: {
    // 预定义的背景色数组
    backgroundColors: [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
      '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
    ],
    showDefaultAvatar: true, // 默认显示缺省头像，等待判断
    avatarText: '',
    backgroundColor: '#3B7CF7',
    sizeClass: 'avatar-medium' // 尺寸样式类
  },

  lifetimes: {
    attached() {
      this.updateSizeClass();
      this.updateAvatar();
    },
    ready() {
      this.updateSizeClass();
      this.updateAvatar();
    }
  },

  observers: {
    'src, name': function (src, name) {
      this.updateAvatar();
    },
    'size': function (size) {
      this.updateSizeClass();
    }
  },

  methods: {
    // 更新尺寸样式类
    updateSizeClass() {
      const { size } = this.properties;
      this.setData({
        sizeClass: `avatar-${size}`
      });
    },

    // 更新头像显示
    updateAvatar() {
      const { src, name } = this.properties;

      if (src && src.trim()) {
        // 有头像URL，显示真实头像
        this.setData({
          showDefaultAvatar: false
        });
      } else {
        // 没有头像URL，显示缺省头像
        this.generateDefaultAvatar(name);
      }
    },

    // 生成缺省头像
    generateDefaultAvatar(name) {
      if (!name || !name.trim()) {
        // 如果没有名称，使用默认文字
        this.setData({
          showDefaultAvatar: true,
          avatarText: '用',
          backgroundColor: '#3B7CF7'
        });
        return;
      }

      // 获取名称的第一个字符
      const firstChar = name.trim().charAt(0).toUpperCase();

      // 根据字符生成背景色（使用字符的ASCII码来选择颜色）
      const charCode = firstChar.charCodeAt(0);
      const colorIndex = charCode % this.data.backgroundColors.length;
      const backgroundColor = this.data.backgroundColors[colorIndex];

      this.setData({
        showDefaultAvatar: true,
        avatarText: firstChar,
        backgroundColor: backgroundColor
      });
    },

    // 头像加载失败处理
    onImageError() {
      console.log('头像加载失败，显示缺省头像');
      this.generateDefaultAvatar(this.properties.name);
    },

    // 头像点击事件
    onAvatarTap() {
      if (this.properties.clickable) {
        this.triggerEvent('avatarTap', {
          src: this.properties.src,
          name: this.properties.name
        });
      }
    }
  }
});
