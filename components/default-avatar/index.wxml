<!-- components/default-avatar/index.wxml -->
<view class="avatar-container {{sizeClass}} {{customClass}}" bindtap="onAvatarTap">
  <!-- 真实头像 -->
  <image
    wx:if="{{src && !showDefaultAvatar}}"
    class="avatar-image"
    src="{{src}}"
    mode="aspectFill"
    binderror="onImageError"
  />

  <!-- 缺省头像 -->
  <view
    wx:else
    class="default-avatar"
    style="background-color: {{backgroundColor}}"
  >
    <text class="avatar-text">{{avatarText}}</text>
  </view>
</view>
