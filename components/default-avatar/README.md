# 缺省头像组件 (Default Avatar)

一个智能的头像组件，当用户头像为空时自动显示基于用户名称首字符的缺省头像。

## 功能特性

- 🖼️ 支持真实头像显示
- 🎨 自动生成缺省头像（基于用户名首字符）
- 🌈 智能背景色生成（基于字符ASCII码）
- 📏 多种尺寸支持（small、medium、large）
- 👆 可选点击交互
- 🔄 头像加载失败自动降级
- 💅 自定义样式类支持

## 使用方法

### 1. 在页面json中引入组件

```json
{
  "usingComponents": {
    "default-avatar": "/components/default-avatar/index"
  }
}
```

### 2. 在wxml中使用

```xml
<!-- 基础用法 -->
<default-avatar 
  src="{{userInfo.avatar}}" 
  name="{{userInfo.name}}" 
  size="medium"
></default-avatar>

<!-- 可点击头像 -->
<default-avatar 
  src="{{userInfo.avatar}}" 
  name="{{userInfo.name}}" 
  size="large"
  clickable="{{true}}"
  bind:avatarTap="onAvatarTap"
></default-avatar>

<!-- 自定义样式 -->
<default-avatar 
  src="{{userInfo.avatar}}" 
  name="{{userInfo.name}}" 
  size="small"
  custom-class="my-avatar"
></default-avatar>
```

### 3. 处理点击事件（可选）

```javascript
Page({
  onAvatarTap(e) {
    const { src, name } = e.detail;
    console.log('头像被点击:', { src, name });
    // 处理头像点击逻辑，如更换头像
  }
})
```

## 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| src | String | '' | 头像图片URL |
| name | String | '' | 用户名称，用于生成缺省头像 |
| size | String | 'medium' | 头像尺寸：small(60rpx)、medium(120rpx)、large(128rpx) |
| custom-class | String | '' | 自定义样式类名 |
| clickable | Boolean | false | 是否可点击 |

## 事件说明

| 事件名 | 说明 | 参数 |
|--------|------|------|
| avatarTap | 头像点击事件（需设置clickable为true） | {src, name} |

## 样式定制

组件提供了基础样式，你可以通过 `custom-class` 属性添加自定义样式：

```css
.my-avatar {
  border: 2rpx solid #3B7CF7;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}
```

## 缺省头像规则

1. 当 `src` 为空或加载失败时，显示缺省头像
2. 缺省头像显示用户名称的第一个字符（转为大写）
3. 背景色根据字符的ASCII码自动选择（15种预设颜色）
4. 如果名称为空，显示默认字符"用"

## 预设背景色

组件内置15种协调的背景色：
- #FF6B6B (红)
- #4ECDC4 (青)  
- #45B7D1 (蓝)
- #96CEB4 (绿)
- #FFEAA7 (黄)
- #DDA0DD (紫)
- #98D8C8 (薄荷)
- #F7DC6F (金)
- #BB8FCE (淡紫)
- #85C1E9 (天蓝)
- #F8C471 (橙)
- #82E0AA (浅绿)
- #F1948A (粉)
- #85C1E9 (浅蓝)
- #D7BDE2 (薰衣草)
