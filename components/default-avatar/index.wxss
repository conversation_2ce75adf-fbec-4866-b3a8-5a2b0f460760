/* components/default-avatar/index.wxss */
.avatar-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.default-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.avatar-text {
  color: white;
  font-weight: 600;
  text-align: center;
  line-height: 1;
}

/* 尺寸样式 */
.avatar-small {
  width: 60rpx;
  height: 60rpx;
}

.avatar-small .avatar-text {
  font-size: 24rpx;
}

.avatar-medium {
  width: 120rpx;
  height: 120rpx;
}

.avatar-medium .avatar-text {
  font-size: 48rpx;
}

.avatar-large {
  width: 128rpx;
  height: 128rpx;
}

.avatar-large .avatar-text {
  font-size: 52rpx;
}

/* 可点击状态 */
.avatar-container[data-clickable="true"] {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.avatar-container[data-clickable="true"]:active {
  transform: scale(0.95);
}
