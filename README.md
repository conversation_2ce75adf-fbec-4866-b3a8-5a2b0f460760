# 互助会单小程序

## 项目概述

这是一款名为"互助会单"的移动端互助金融工具，实现传统标会流程的线上化运作。

## 已完成功能

### 1. 首页 (pages/index)
- 会单列表展示
- 搜索功能
- 分类筛选（全部/我创建的/我参与的）
- 下拉刷新和上拉加载更多
- 会东信息弹窗

### 2. 会单详情页 (pages/detail)
- 会单基本信息展示
- 即将到期会期提醒
- 会东信息
- 会员列表
- 会期详情表格
- 收付款记录时间轴
- 资金统计
- 自定义数字键盘投标功能
- 底部操作按钮

### 3. 统计页 (pages/stats) ✨ 新增
- **顶部概览卡片**：展示参与会单数、全年收支、预计支出
- **收支趋势图表**：可切换近6月/近1年/全部时间段
- **会单参与统计**：创建的/参与的/已完成/进行中会单数量
- **月度收支明细**：可选择月份查看详细收支记录
- **年度报告**：最活跃月份、最大单笔收入、合作次数最多等统计

### 4. 我的页面 (pages/my) ✨ 新增
- **用户信息头部**：头像、姓名、联系方式、信用等级
- **用户统计概览**：参与会单、已完成、信用分
- **我的会单管理**：可切换查看创建的/参与的/已完成会单
- **功能菜单**：账户设置、安全中心、消息通知、帮助反馈
- **其他功能**：推荐给朋友、关于我们、检查更新
- **退出登录**功能

## 设计特色

### 视觉风格
- **主色调**：#3b7cf7（蓝色），体现金融属性的专业感
- **卡片式设计**：16rpx圆角，营造清新简洁的视觉效果
- **渐变背景**：统计页和我的页面采用渐变色头部，提升视觉层次
- **阴影效果**：0 4px 12px rgba(0, 0, 0, 0.05)，增强卡片立体感

### 交互体验
- **统一的间距规范**：30rpx内边距，20rpx外边距
- **状态反馈**：不同状态用不同颜色标识（进行中/已完成/待开始）
- **数据可视化**：简洁的柱状图展示收支趋势
- **响应式布局**：适配不同屏幕尺寸

### 功能亮点
- **智能统计**：自动计算收支趋势和预测
- **多维度数据**：从时间、类型、状态等多角度展示数据
- **用户中心**：完整的个人信息管理和会单管理
- **金融级体验**：专业的数据展示和操作反馈

## 技术实现

- **框架**：微信小程序原生开发
- **样式**：WXSS，采用Flexbox布局
- **数据管理**：Page数据绑定，模拟API数据
- **交互**：事件绑定，状态管理

## 文件结构

```
pages/
├── index/          # 首页
├── detail/         # 会单详情页
├── stats/          # 统计页 ✨
└── my/             # 我的页面 ✨
```

## 使用说明

1. 在微信开发者工具中打开项目
2. 确保appid配置正确
3. 编译运行即可预览效果

## 后续开发建议

1. 接入真实API数据
2. 添加用户认证和权限管理
3. 实现推送通知功能
4. 添加更多图表类型
5. 完善错误处理和加载状态
